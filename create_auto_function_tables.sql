-- SQL script to create auto_function tables with soft delete functionality
-- This script creates the tables from scratch with is_deleted columns included

-- Drop existing tables if they exist (optional - uncomment if needed)
-- DROP TABLE IF EXISTS auto_function_customers CASCADE;
-- DROP TABLE IF EXISTS auto_function CASCADE;

-- Create auto_function table
CREATE TABLE auto_function (
    id SERIAL PRIMARY KEY,
    code VA<PERSON>HA<PERSON>(255) NOT NULL UNIQUE,
    name VA<PERSON>HAR(500) NOT NULL,
    note TEXT,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create auto_function_customers junction table
CREATE TABLE auto_function_customers (
    id SERIAL PRIMARY KEY,
    auto_function_id INTEGER NOT NULL,
    customer_id INTEGER NOT NULL,
    is_deleted BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_auto_function_customers_auto_function 
        FOREIGN KEY (auto_function_id) REFERENCES auto_function(id) ON DELETE CASCADE,
    CONSTRAINT fk_auto_function_customers_customer 
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate relationships
    CONSTRAINT uk_auto_function_customers_unique 
        UNIQUE(auto_function_id, customer_id)
);

-- Create indexes for better performance
CREATE INDEX idx_auto_function_code ON auto_function(code);
CREATE INDEX idx_auto_function_name ON auto_function(name);
CREATE INDEX idx_auto_function_is_deleted ON auto_function(is_deleted);
CREATE INDEX idx_auto_function_created_at ON auto_function(created_at);

-- Indexes for auto_function_customers table
CREATE INDEX idx_auto_function_customers_auto_function_id ON auto_function_customers(auto_function_id);
CREATE INDEX idx_auto_function_customers_customer_id ON auto_function_customers(customer_id);
CREATE INDEX idx_auto_function_customers_is_deleted ON auto_function_customers(is_deleted);

-- Composite indexes for common query patterns
CREATE INDEX idx_auto_function_customers_function_is_deleted 
    ON auto_function_customers(auto_function_id, is_deleted);
CREATE INDEX idx_auto_function_customers_customer_is_deleted 
    ON auto_function_customers(customer_id, is_deleted);
CREATE INDEX idx_auto_function_customers_both_is_deleted 
    ON auto_function_customers(auto_function_id, customer_id, is_deleted);

-- Add comments for documentation
COMMENT ON TABLE auto_function IS 'Auto function definitions for automated calculations';
COMMENT ON COLUMN auto_function.id IS 'Primary key - auto increment';
COMMENT ON COLUMN auto_function.code IS 'Unique code identifier for the auto function';
COMMENT ON COLUMN auto_function.name IS 'Display name of the auto function';
COMMENT ON COLUMN auto_function.note IS 'Additional notes or description';
COMMENT ON COLUMN auto_function.is_deleted IS 'Soft delete flag - TRUE means record is deleted';
COMMENT ON COLUMN auto_function.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN auto_function.updated_at IS 'Timestamp when record was last updated';

COMMENT ON TABLE auto_function_customers IS 'Junction table linking auto functions to customers';
COMMENT ON COLUMN auto_function_customers.id IS 'Primary key - auto increment';
COMMENT ON COLUMN auto_function_customers.auto_function_id IS 'Foreign key to auto_function table';
COMMENT ON COLUMN auto_function_customers.customer_id IS 'Foreign key to customers table';
COMMENT ON COLUMN auto_function_customers.is_deleted IS 'Soft delete flag - TRUE means relationship is deleted';
COMMENT ON COLUMN auto_function_customers.created_at IS 'Timestamp when relationship was created';
COMMENT ON COLUMN auto_function_customers.updated_at IS 'Timestamp when relationship was last updated';

-- Create trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to auto_function table
CREATE TRIGGER update_auto_function_updated_at 
    BEFORE UPDATE ON auto_function 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Apply trigger to auto_function_customers table
CREATE TRIGGER update_auto_function_customers_updated_at 
    BEFORE UPDATE ON auto_function_customers 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert initial seed data (17 Nitori auto functions)
-- First, find or create Nitori customer (assuming customers table exists)
DO $$
DECLARE
    nitori_customer_id INTEGER;
    auto_func_id INTEGER;
BEGIN
    -- Try to find Nitori customer
    SELECT id INTO nitori_customer_id 
    FROM customers 
    WHERE code = 'CUS002' OR name = 'Nitori' 
    LIMIT 1;
    
    -- If Nitori customer not found, you may need to create it first
    -- or modify this script to use a different customer_id
    
    IF nitori_customer_id IS NOT NULL THEN
        -- Insert auto functions and link to Nitori customer
        
        -- 1. Nitory Inbound Auto Calculation Total Cartons Received
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_IN_SUM_CARTON', 'Nitory Inbound Auto Calculation Total Cartons Received', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 2. Nitory Inbound Auto Calculation Total Pieces Received
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_IN_SUM_PCS', 'Nitory Inbound Auto Calculation Total Pieces Received', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 3. Nitory Inbound Auto Calculation Total AI Pieces
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_IN_SUM_AI', 'Nitory Inbound Auto  Calculation Total AI Pieces', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 4. Nitory Inbound Auto Calculation Total m3
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_IN_SUM_M3', 'Nitory Inbound Auto Calculation  Total m3', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 5. Nitory Outbound Auto Calculation Total Cartons Received
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_ALL_SUM_CARTON', 'Nitory Outbound Auto  Calculation Total Cartons Received', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 6. Nitory Outbound Auto Calculation Total Pieces Received
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_ALL_SUM_PCS', 'Nitory Outbound Auto  Calculation Total Pieces Received', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 7. Nitory Outbound Auto Calculation Total m3
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_ALL_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 8. Nitory Outbound Auto Calculation Total Cartons Received for Store
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_STORE_SUM_CARTON', 'Nitory Outbound Auto Calculation Total Cartons Received for Store', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 9. Nitory Outbound Auto Calculation Total Pieces Received for Store
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_STORE_SUM_PCS', 'Nitory Outbound Auto Calculation Total Pieces Received for Store', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 10. Nitory Outbound Auto Calculation Total m3 for Store
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_STORE_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3 for Store', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 11. Nitory Outbound Auto Calculation Total Cartons Received for Customer
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_CUSTOMER_SUM_CARTON', 'Nitory Outbound Auto Calculation Total Cartons Received for Customer', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 12. Nitory Outbound Auto Calculation Total Pieces Received for Customer
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_CUSTOMER_SUM_PCS', 'Nitory Outbound Auto Calculation Total Pieces Received for Customer', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 13. Nitory Outbound Auto Calculation Total m3 for Customer
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_CUSTOMER_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3 for Customer', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 14. Nitory Outbound Auto Calculation Total Labeling
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_CUSTOMER_SUM_LABEL', 'Nitory Outbound Auto Calculation Total Labeling', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 15. Nitory Outbound Auto Calculation Total Cartons without COD
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_NONE_SUM_CARTON', 'Nitory Outbound Auto  Calculation Total Cartons without COD', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 16. Nitory Outbound Auto Calculation Total Pieces without COD
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_NONE_SUM_PCS', 'Nitory Outbound Auto  Calculation Total Pieces without COD', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 17. Nitory Outbound Auto Calculation Total m3 without COD
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_NONE_SUM_M3', 'Nitory Outbound Auto Calculation Total m3 without COD', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        RAISE NOTICE 'Successfully created 17 auto functions and linked them to Nitori customer (ID: %)', nitori_customer_id;
    ELSE
        RAISE NOTICE 'Nitori customer not found. Auto functions created without customer links.';
        RAISE NOTICE 'You may need to manually link auto functions to customers later.';
        
        -- Insert auto functions without customer links
        INSERT INTO auto_function (code, name, note) VALUES
        ('N_A_IN_SUM_CARTON', 'Nitory Inbound Auto Calculation Total Cartons Received', 'For Nitory Only'),
        ('N_A_IN_SUM_PCS', 'Nitory Inbound Auto Calculation Total Pieces Received', 'For Nitory Only'),
        ('N_A_IN_SUM_AI', 'Nitory Inbound Auto  Calculation Total AI Pieces', 'For Nitory Only'),
        ('N_A_IN_SUM_M3', 'Nitory Inbound Auto Calculation  Total m3', 'For Nitory Only'),
        ('N_A_OUT_ALL_SUM_CARTON', 'Nitory Outbound Auto  Calculation Total Cartons Received', 'For Nitory Only'),
        ('N_A_OUT_ALL_SUM_PCS', 'Nitory Outbound Auto  Calculation Total Pieces Received', 'For Nitory Only'),
        ('N_A_OUT_ALL_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3', 'For Nitory Only'),
        ('N_A_OUT_STORE_SUM_CARTON', 'Nitory Outbound Auto Calculation Total Cartons Received for Store', 'For Nitory Only'),
        ('N_A_OUT_STORE_SUM_PCS', 'Nitory Outbound Auto Calculation Total Pieces Received for Store', 'For Nitory Only'),
        ('N_A_OUT_STORE_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3 for Store', 'For Nitory Only'),
        ('N_A_OUT_CUSTOMER_SUM_CARTON', 'Nitory Outbound Auto Calculation Total Cartons Received for Customer', 'For Nitory Only'),
        ('N_A_OUT_CUSTOMER_SUM_PCS', 'Nitory Outbound Auto Calculation Total Pieces Received for Customer', 'For Nitory Only'),
        ('N_A_OUT_CUSTOMER_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3 for Customer', 'For Nitory Only'),
        ('N_A_OUT_CUSTOMER_SUM_LABEL', 'Nitory Outbound Auto Calculation Total Labeling', 'For Nitory Only'),
        ('N_A_OUT_NONE_SUM_CARTON', 'Nitory Outbound Auto  Calculation Total Cartons without COD', 'For Nitory Only'),
        ('N_A_OUT_NONE_SUM_PCS', 'Nitory Outbound Auto  Calculation Total Pieces without COD', 'For Nitory Only'),
        ('N_A_OUT_NONE_SUM_M3', 'Nitory Outbound Auto Calculation Total m3 without COD', 'For Nitory Only');
    END IF;
END $$;

-- Verify the tables were created successfully
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('auto_function', 'auto_function_customers')
ORDER BY table_name, ordinal_position;

-- Show count of created records
SELECT 
    'auto_function' as table_name,
    COUNT(*) as record_count
FROM auto_function
UNION ALL
SELECT 
    'auto_function_customers' as table_name,
    COUNT(*) as record_count
FROM auto_function_customers;

-- Show sample data
SELECT 
    af.id,
    af.code,
    af.name,
    af.is_deleted,
    COUNT(afc.id) as customer_count
FROM auto_function af
LEFT JOIN auto_function_customers afc ON af.id = afc.auto_function_id AND afc.is_deleted = FALSE
WHERE af.is_deleted = FALSE
GROUP BY af.id, af.code, af.name, af.is_deleted
ORDER BY af.id
LIMIT 5;
