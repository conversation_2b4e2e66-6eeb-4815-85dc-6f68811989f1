from sqlalchemy import create_engine, text
from config import Config
import json
import uuid
from datetime import datetime

class WorkoutChargeDAO:
    def __init__(self):
        self.engine = create_engine(Config.get_db_uri())

    def generate_workout_charge_id(self):
        """Generate unique ID for workout charge"""
        timestamp = int(datetime.now().timestamp() * 1000)
        random_part = str(uuid.uuid4()).replace('-', '')[:9]
        return f"WCC_{timestamp}_{random_part}"

    def get_workout_charge_by_id(self, charge_id):
        """Get workout customer charge by ID"""
        sql = """
            SELECT id, customer_id, division_id, category_id, workout_id, year,
                   month_1, month_2, month_3, month_4, month_5, month_6,
                   month_7, month_8, month_9, month_10, month_11, month_12,
                   created_at, updated_at
            FROM workout_customer_charge 
            WHERE id = :id
        """
        with self.engine.connect() as conn:
            result = conn.execute(text(sql), {'id': charge_id})
            rows = result.fetchall()
            if not rows:
                return None
            
            return self._convert_flat_to_hierarchical(rows)

    def get_workout_charges(self, filters=None):
        """Get all workout customer charges with optional filters"""
        where_clauses = []
        params = {}
        
        if filters:
            if filters.get('customer_id'):
                where_clauses.append('customer_id = :customer_id')
                params['customer_id'] = filters['customer_id']
            if filters.get('division_id'):
                where_clauses.append('division_id = :division_id')
                params['division_id'] = filters['division_id']
        
        where_clause = f"WHERE {' AND '.join(where_clauses)}" if where_clauses else ""
        
        sql = f"""
            SELECT id, customer_id, division_id, category_id, workout_id, year,
                   month_1, month_2, month_3, month_4, month_5, month_6,
                   month_7, month_8, month_9, month_10, month_11, month_12,
                   created_at, updated_at
            FROM workout_customer_charge 
            {where_clause}
            ORDER BY created_at DESC
        """
        
        with self.engine.connect() as conn:
            result = conn.execute(text(sql), params)
            rows = result.fetchall()
            
            if not rows:
                return []
            
            # Group by customer/division and convert to hierarchical
            grouped_charges = {}
            for row in rows:
                key = f"{row.customer_id}_{row.division_id or 'null'}"
                if key not in grouped_charges:
                    grouped_charges[key] = []
                grouped_charges[key].append(row)
            
            result_charges = []
            for group_rows in grouped_charges.values():
                hierarchical_charge = self._convert_flat_to_hierarchical(group_rows)
                if hierarchical_charge:
                    result_charges.append(hierarchical_charge)
            
            return result_charges

    def get_workout_charge_by_customer_division(self, customer_id, division_id):
        """Get workout customer charge by customer and division"""
        # Handle division_id = 0 as NULL
        if division_id is None or division_id == 0:
            where_clause = "WHERE customer_id = :customer_id AND division_id IS NULL"
            params = {'customer_id': customer_id}
        else:
            where_clause = "WHERE customer_id = :customer_id AND division_id = :division_id"
            params = {'customer_id': customer_id, 'division_id': division_id}
        
        sql = f"""
            SELECT id, customer_id, division_id, category_id, workout_id, year,
                   month_1, month_2, month_3, month_4, month_5, month_6,
                   month_7, month_8, month_9, month_10, month_11, month_12,
                   created_at, updated_at
            FROM workout_customer_charge 
            {where_clause}
        """
        
        with self.engine.connect() as conn:
            result = conn.execute(text(sql), params)
            rows = result.fetchall()
            
            if not rows:
                return None
            
            return self._convert_flat_to_hierarchical(rows)

    def _convert_flat_to_hierarchical(self, rows):
        """Convert flat database rows to hierarchical structure"""
        if not rows:
            return None
        
        first_row = rows[0]
        hierarchical_data = {
            'id': first_row.id,
            'customer_id': first_row.customer_id,
            'division_id': first_row.division_id,
            'workout_customer_charge_content': {},
            'created_at': str(first_row.created_at),
            'updated_at': str(first_row.updated_at)
        }
        
        for row in rows:
            category_key = f"category_{row.category_id}"
            workout_key = f"workout_{row.workout_id}"
            year_key = str(row.year)
            
            # Initialize category if not exists
            if category_key not in hierarchical_data['workout_customer_charge_content']:
                hierarchical_data['workout_customer_charge_content'][category_key] = {
                    'category_id': row.category_id,
                    'category_name': self._get_category_name(row.category_id),
                    'workouts': {}
                }
            
            # Initialize workout if not exists
            if workout_key not in hierarchical_data['workout_customer_charge_content'][category_key]['workouts']:
                workout_info = self._get_workout_info(row.workout_id)
                hierarchical_data['workout_customer_charge_content'][category_key]['workouts'][workout_key] = {
                    'group_id': row.workout_id,
                    'group_name': workout_info.get('name', ''),
                    'group_code': workout_info.get('code', ''),
                    'unit_price': workout_info.get('unit_price', '0'),
                    'unit_price_unit': workout_info.get('unit_price_unit', 'VND'),
                    'unit_price_sign': workout_info.get('unit_price_sign', '+'),
                    'unit_price_is_percentage': workout_info.get('unit_price_is_percentage', False),
                    'vat': workout_info.get('vat', '0'),
                    'years': {}
                }
            
            # Initialize year if not exists
            if year_key not in hierarchical_data['workout_customer_charge_content'][category_key]['workouts'][workout_key]['years']:
                hierarchical_data['workout_customer_charge_content'][category_key]['workouts'][workout_key]['years'][year_key] = {}
            
            # Add month data (including zero quantities)
            for month in range(1, 13):
                month_value = getattr(row, f'month_{month}')
                if month_value is not None:  # Include zero quantities
                    month_key = f"month_{month}"
                    hierarchical_data['workout_customer_charge_content'][category_key]['workouts'][workout_key]['years'][year_key][month_key] = {
                        'month': month,
                        'quantity': month_value
                    }
        
        return hierarchical_data

    def _get_category_name(self, category_id):
        """Get category name by ID"""
        try:
            sql = "SELECT name FROM categories WHERE id = :id"
            with self.engine.connect() as conn:
                result = conn.execute(text(sql), {'id': category_id})
                row = result.fetchone()
                return row.name if row else ''
        except:
            return ''

    def _get_workout_info(self, workout_id):
        """Get workout name and price by ID"""
        try:
            sql = """
                SELECT w.name, w.code, w.unit_price, w.unit_price_unit,
                       w.unit_price_sign, w.unit_price_is_percentage, w.vat
                FROM workout w
                WHERE w.id = :id
            """
            with self.engine.connect() as conn:
                result = conn.execute(text(sql), {'id': workout_id})
                row = result.fetchone()
                if row:
                    return {
                        'name': row.name,
                        'code': row.code,
                        'unit_price': str(row.unit_price) if row.unit_price is not None else '0',
                        'unit_price_unit': row.unit_price_unit or 'VND',
                        'unit_price_sign': row.unit_price_sign or '+',
                        'unit_price_is_percentage': row.unit_price_is_percentage or False,
                        'vat': str(row.vat) if row.vat is not None else '0'
                    }

            return {
                'name': '',
                'code': '',
                'unit_price': '0',
                'unit_price_unit': 'VND',
                'unit_price_sign': '+',
                'unit_price_is_percentage': False,
                'vat': '0'
            }
        except Exception as e:
            print(f"Error getting workout info for ID {workout_id}: {str(e)}")
            return {
                'name': '',
                'code': '',
                'unit_price': '0',
                'unit_price_unit': 'VND',
                'unit_price_sign': '+',
                'unit_price_is_percentage': False,
                'vat': '0'
            }

    def create_workout_charge(self, data):
        """Create new workout customer charge (append to existing data)"""
        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    # Convert hierarchical data to flat records and insert
                    flat_records = self._convert_hierarchical_to_flat(data)

                    # If no flat records (all years empty), still consider it successful
                    # This allows frontend to create structure without month data
                    if not flat_records:
                        return True

                    insert_sql = """
                        INSERT INTO workout_customer_charge (
                            id, customer_id, division_id, category_id, workout_id, year,
                            month_1, month_2, month_3, month_4, month_5, month_6,
                            month_7, month_8, month_9, month_10, month_11, month_12,
                            created_at, updated_at
                        ) VALUES (
                            :id, :customer_id, :division_id, :category_id, :workout_id, :year,
                            :month_1, :month_2, :month_3, :month_4, :month_5, :month_6,
                            :month_7, :month_8, :month_9, :month_10, :month_11, :month_12,
                            :created_at, :updated_at
                        )
                        ON CONFLICT (id) DO NOTHING
                    """

                    inserted_count = 0
                    for record in flat_records:
                        result = conn.execute(text(insert_sql), record)
                        if result.rowcount > 0:
                            inserted_count += 1

                    if inserted_count == 0 and flat_records:
                        raise ValueError("No new records were created. All records may already exist.")

                    return True
        except Exception as e:
            print(f"Error creating workout charge: {str(e)}")
            raise e

    def replace_workout_charge(self, data):
        """Replace all workout customer charge data for a customer/division (UPSERT logic)"""
        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    # Delete existing records for this customer/division
                    division_id = data.get('division_id')
                    if division_id and division_id != 0:
                        delete_sql = """
                            DELETE FROM workout_customer_charge
                            WHERE customer_id = :customer_id AND division_id = :division_id
                        """
                        conn.execute(text(delete_sql), {
                            'customer_id': data['customer_id'],
                            'division_id': division_id
                        })
                    else:
                        delete_sql = """
                            DELETE FROM workout_customer_charge
                            WHERE customer_id = :customer_id AND division_id IS NULL
                        """
                        conn.execute(text(delete_sql), {'customer_id': data['customer_id']})

                    # Convert hierarchical data to flat records and insert
                    flat_records = self._convert_hierarchical_to_flat(data)

                    # If no flat records (all years empty), still consider it successful
                    # This allows frontend to create structure without month data
                    if not flat_records:
                        return True

                    insert_sql = """
                        INSERT INTO workout_customer_charge (
                            id, customer_id, division_id, category_id, workout_id, year,
                            month_1, month_2, month_3, month_4, month_5, month_6,
                            month_7, month_8, month_9, month_10, month_11, month_12,
                            created_at, updated_at
                        ) VALUES (
                            :id, :customer_id, :division_id, :category_id, :workout_id, :year,
                            :month_1, :month_2, :month_3, :month_4, :month_5, :month_6,
                            :month_7, :month_8, :month_9, :month_10, :month_11, :month_12,
                            :created_at, :updated_at
                        )
                    """

                    for record in flat_records:
                        conn.execute(text(insert_sql), record)

                    return True
        except Exception as e:
            print(f"Error replacing workout charge: {str(e)}")
            raise e

    def update_workout_charge(self, charge_id, data):
        """Update workout customer charge (replace all data for customer/division)"""
        # For now, update means replace all data for the customer/division
        # This maintains the original TypeScript behavior
        return self.replace_workout_charge(data)

    def delete_workout_charge(self, charge_id):
        """Delete workout customer charge by ID"""
        sql = "DELETE FROM workout_customer_charge WHERE id = :id"
        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    result = conn.execute(text(sql), {'id': charge_id})
                    return result.rowcount > 0
        except Exception as e:
            print(f"Error deleting workout charge: {str(e)}")
            raise e

    def _convert_hierarchical_to_flat(self, data):
        """Convert hierarchical data to flat records for database storage"""
        flat_records = []
        now = datetime.now().isoformat()

        content = data.get('workout_customer_charge_content', {})

        for category_key, category_data in content.items():
            for workout_key, workout_data in category_data.get('workouts', {}).items():
                years = workout_data.get('years', {})

                # Process all years (including those with zero quantities)
                for year, year_data in years.items():
                    # Handle division_id - if 0 or None, set to None for database
                    division_id = data.get('division_id')
                    if division_id == 0:
                        division_id = None

                    # If year is empty {}, create all 12 months with quantity = 0
                    if not year_data or len(year_data) == 0:
                        record = {
                            'id': self.generate_workout_charge_id(),
                            'customer_id': data['customer_id'],
                            'division_id': division_id,
                            'category_id': category_data['category_id'],
                            'workout_id': workout_data['group_id'],
                            'year': int(year),
                            'created_at': now,
                            'updated_at': now
                        }

                        # Initialize all months to 0 for empty years
                        for month in range(1, 13):
                            record[f'month_{month}'] = 0

                        flat_records.append(record)
                        continue

                    # Check if year has any month data (including zero quantities)
                    has_month_structure = False
                    for month_key, month_data in year_data.items():
                        if month_key.startswith('month_') and 'quantity' in month_data:
                            has_month_structure = True
                            break

                    # Skip year if no month structure found
                    if not has_month_structure:
                        continue

                    record = {
                        'id': self.generate_workout_charge_id(),
                        'customer_id': data['customer_id'],
                        'division_id': division_id,
                        'category_id': category_data['category_id'],
                        'workout_id': workout_data['group_id'],
                        'year': int(year),
                        'created_at': now,
                        'updated_at': now
                    }

                    # Initialize all months to None
                    for month in range(1, 13):
                        record[f'month_{month}'] = None

                    # Set month quantities from year_data (including zero quantities)
                    for month_key, month_data in year_data.items():
                        if month_key.startswith('month_'):
                            month_num = month_data.get('month')
                            if month_num and 1 <= month_num <= 12:
                                record[f'month_{month_num}'] = month_data.get('quantity', 0)

                    flat_records.append(record)

        return flat_records

    # Dropdown data methods
    def get_customers_for_dropdown(self):
        """Get customers for dropdown"""
        sql = """
            SELECT id, name, code
            FROM customers
            WHERE status = 'active'
            ORDER BY name
        """
        with self.engine.connect() as conn:
            result = conn.execute(text(sql))
            customers = []
            for row in result:
                customers.append({
                    'id': row.id,
                    'name': row.name,
                    'code': row.code
                })
            return customers

    def get_divisions_by_customer_id(self, customer_id):
        """Get divisions by customer ID for dropdown"""
        sql = """
            SELECT id, name, code, customer_id
            FROM customer_divisions
            WHERE customer_id = :customer_id AND status = 'active'
            ORDER BY name
        """
        with self.engine.connect() as conn:
            result = conn.execute(text(sql), {'customer_id': customer_id})
            divisions = []
            for row in result:
                divisions.append({
                    'id': row.id,
                    'name': row.name,
                    'code': row.code,
                    'customer_id': row.customer_id
                })
            return divisions

    def get_categories_for_dropdown(self):
        """Get categories for dropdown"""
        try:
            sql = """
                SELECT id, code, name
                FROM categories
                WHERE status = 'active'
                ORDER BY name
            """
            with self.engine.connect() as conn:
                result = conn.execute(text(sql))
                categories = []
                for row in result:
                    categories.append({
                        'id': str(row.id),
                        'code': row.code,
                        'name': row.name
                    })
                return categories
        except Exception as e:
            print(f"Error getting categories: {str(e)}")
            # Return mock data if database query fails
            return [
                {'id': '1', 'code': 'CAT001', 'name': 'Fitness Training'},
                {'id': '2', 'code': 'CAT002', 'name': 'Strength Training'},
                {'id': '3', 'code': 'CAT003', 'name': 'Cardio Workout'},
                {'id': '4', 'code': 'CAT004', 'name': 'Yoga & Meditation'},
                {'id': '5', 'code': 'CAT005', 'name': 'Sports Training'}
            ]

    def get_all_categories(self):
        """Get all categories with detailed information"""
        try:
            sql = """
                SELECT id, code, name, status
                FROM categories
                ORDER BY name
            """
            with self.engine.connect() as conn:
                result = conn.execute(text(sql))
                categories = []
                for row in result:
                    category = {
                        'id': str(row.id),
                        'code': row.code,
                        'name': row.name,
                        'status': row.status
                    }
                    categories.append(category)
                return categories
        except Exception as e:
            print(f"Error getting all categories: {str(e)}")
            # Return mock data with detailed information if database query fails
            return [
                {
                    'id': '1',
                    'code': 'CAT001',
                    'name': 'Fitness Training',
                    'status': 'active'
                },
                {
                    'id': '2',
                    'code': 'CAT002',
                    'name': 'Strength Training',
                    'status': 'active'
                },
                {
                    'id': '3',
                    'code': 'CAT003',
                    'name': 'Cardio Workout',
                    'status': 'active'
                },
                {
                    'id': '4',
                    'code': 'CAT004',
                    'name': 'Yoga & Meditation',
                    'status': 'active'
                },
                {
                    'id': '5',
                    'code': 'CAT005',
                    'name': 'Sports Training',
                    'status': 'active'
                },
                {
                    'id': '6',
                    'code': 'CAT006',
                    'name': 'Rehabilitation',
                    'status': 'active'
                }
            ]

    def get_category_workouts_by_category_id(self, category_id):
        """Get category workouts by category ID for dropdown"""
        try:
            sql = """
                SELECT w.id, w.code, w.name, w.unit_price, w.unit_price_unit,
                       w.unit_price_sign, w.unit_price_is_percentage, w.status, w.vat, w.calculation_type
                FROM workout w
                WHERE w.category_id = :category_id
                AND w.status = 'active'
                ORDER BY w.name
            """
            with self.engine.connect() as conn:
                result = conn.execute(text(sql), {'category_id': category_id})
                workouts = []
                for row in result:
                    workout = {
                        'id': str(row.id),
                        'code': row.code,
                        'name': row.name,
                        'status': row.status,
                        'category_id': str(category_id)
                    }

                    # Add price information if available
                    if row.unit_price is not None:
                        workout['unit_price'] = str(row.unit_price)
                        workout['unit_price_unit'] = row.unit_price_unit or 'VND'
                        workout['unit_price_sign'] = row.unit_price_sign or '+'
                        workout['unit_price_is_percentage'] = row.unit_price_is_percentage or False

                    # Add VAT information
                    if row.vat is not None:
                        workout['vat'] = str(row.vat)

                    workouts.append(workout)
                return workouts
        except Exception as e:
            print(f"Error getting workouts for category {category_id}: {str(e)}")
            # Return mock data if database query fails
            return [
                {
                    'id': '1',
                    'code': 'WORKOUT_FIT_001',
                    'name': 'Basic Fitness Program',
                    'unit_price': '50000.00',
                    'unit_price_unit': 'VND',
                    'unit_price_sign': '+',
                    'unit_price_is_percentage': False,
                    'status': 'active',
                    'vat': '0.0',
                    'category_id': str(category_id)
                },
                {
                    'id': '2',
                    'code': 'WORKOUT_FIT_002',
                    'name': 'Advanced Fitness Program',
                    'unit_price': '75000.00',
                    'unit_price_unit': 'VND',
                    'unit_price_sign': '+',
                    'unit_price_is_percentage': False,
                    'status': 'active',
                    'vat': '0.0',
                    'category_id': str(category_id)
                }
            ]
