-- Migration script to add is_deleted column to tables for soft delete functionality
-- Run this script to add is_deleted columns to existing tables

-- Add is_deleted column to customer table
ALTER TABLE customer 
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

-- Add is_deleted column to customer_divisions table
ALTER TABLE customer_divisions 
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

-- Add is_deleted column to customer_settings table
ALTER TABLE customer_settings 
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

-- Add is_deleted column to workout_customer_charge table
ALTER TABLE workout_customer_charge 
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

-- Create indexes for better performance on is_deleted queries
CREATE INDEX IF NOT EXISTS idx_customer_is_deleted ON customer(is_deleted);
CREATE INDEX IF NOT EXISTS idx_customer_divisions_is_deleted ON customer_divisions(is_deleted);
CREATE INDEX IF NOT EXISTS idx_customer_settings_is_deleted ON customer_settings(is_deleted);
CREATE INDEX IF NOT EXISTS idx_workout_customer_charge_is_deleted ON workout_customer_charge(is_deleted);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_customer_divisions_customer_is_deleted 
ON customer_divisions(customer_id, is_deleted);

CREATE INDEX IF NOT EXISTS idx_customer_settings_customer_is_deleted 
ON customer_settings(customer_id, is_deleted);

CREATE INDEX IF NOT EXISTS idx_workout_customer_charge_customer_division_is_deleted 
ON workout_customer_charge(customer_id, division_id, is_deleted);

-- Comments for documentation
COMMENT ON COLUMN customer.is_deleted IS 'Soft delete flag - TRUE means record is deleted';
COMMENT ON COLUMN customer_divisions.is_deleted IS 'Soft delete flag - TRUE means record is deleted';
COMMENT ON COLUMN customer_settings.is_deleted IS 'Soft delete flag - TRUE means record is deleted';
COMMENT ON COLUMN workout_customer_charge.is_deleted IS 'Soft delete flag - TRUE means record is deleted';

-- Verify the changes
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('customer', 'customer_divisions', 'customer_settings', 'workout_customer_charge')
AND column_name = 'is_deleted'
ORDER BY table_name;
