# Workout Customer Charge API Implementation

Đây là implementation của Workout Customer Charge API dựa trên các function trong `service_code.ts`, đ<PERSON><PERSON><PERSON> tích hợp vào Flask API hiện tại.

## 📁 Files đã tạo/cập nhật

### 1. Data Access Layer (DAO)
- `app/dao/workout_charge_dao.py` - <PERSON><PERSON><PERSON> tất cả SQL queries và data conversion logic

### 2. Business Logic Layer (Service)
- `app/services/workout_charge_service.py` - Chứa business logic và validation

### 3. API Layer (Controller)
- `app/controllers/workout_charge_controller.py` - Chứa tất cả API endpoints
- `app/controllers/__init__.py` - <PERSON><PERSON> cập nhật để register Workout Charge blueprint

### 4. Database
- `database/workout_charge_tables.sql` - Script tạo bảng và sample data

### 5. Documentation & Testing
- `WORKOUT_CHARGE_API_DOCUMENTATION.md` - <PERSON><PERSON><PERSON> liệu chi tiết về API endpoints
- `test_workout_charge_api.py` - Script test tự động
- `WORKOUT_CHARGE_API_README.md` - File này

## 🚀 Cách chạy

### 1. Chuẩn bị Database

**Option A: Automatic Setup (Recommended)**
```bash
python setup_workout_charge_api.py
```

**Option B: Manual Setup**
```sql
-- Bước 1: Tạo customer tables trước (nếu chưa có)
psql -d your_database -f database/customer_tables.sql

-- Bước 2: Tạo workout charge tables
psql -d your_database -f database/workout_charge_tables.sql

-- Bước 3: Thêm sample data (optional)
psql -d your_database -f database/insert_workout_charge_sample_data.sql
```

### 2. Cài đặt Dependencies
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows

# Install dependencies (đã có sẵn từ requirements.txt)
pip install -r requirements.txt
```

### 3. Chạy API Server
```bash
python run.py
```

Server sẽ chạy tại: `http://localhost:5000`

## 📋 API Endpoints

### Main Workout Charge Management
- `GET /workout-charges/` - Lấy tất cả workout charges (với filters)
- `GET /workout-charges/{id}` - Lấy workout charge theo ID
- `GET /workout-charges/customer/{customer_id}/division/{division_id}` - Lấy theo customer/division
- `POST /workout-charges/` - Tạo workout charge mới
- `PUT /workout-charges/{id}` - Cập nhật workout charge
- `DELETE /workout-charges/{id}` - Xóa workout charge

### Dropdown Data
- `GET /workout-charges/customers` - Lấy customers cho dropdown
- `GET /workout-charges/customers/{id}/divisions` - Lấy divisions cho dropdown
- `GET /workout-charges/categories` - Lấy categories cho dropdown
- `GET /workout-charges/categories/{id}/workouts` - Lấy workouts cho dropdown

## 🧪 Testing

### 1. Automatic Testing
```bash
python test_workout_charge_api.py
```

### 2. Manual Testing với curl

#### Đăng nhập để lấy token:
```bash
curl -X POST http://localhost:5000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

#### Lấy categories:
```bash
curl -X GET http://localhost:5000/workout-charges/categories \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Tạo workout charge:
```bash
curl -X POST http://localhost:5000/workout-charges/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "customer_id": 1,
    "division_id": 1,
    "workout_customer_charge_content": {
      "category_CAT001": {
        "category_id": "CAT001",
        "workouts": {
          "workout_WORKOUT_FIT_001": {
            "group_id": "WORKOUT_FIT_001",
            "years": {
              "2024": {
                "month_1": {"month": 1, "quantity": 10},
                "month_2": {"month": 2, "quantity": 12}
              }
            }
          }
        }
      }
    }
  }'
```

## 🏗️ Architecture

### Mapping từ TypeScript sang Python

| TypeScript Function | Python Endpoint | HTTP Method |
|---------------------|-----------------|-------------|
| `getWorkoutCustomerChargeById(id)` | `/workout-charges/{id}` | GET |
| `getWorkoutCustomerCharges(filters)` | `/workout-charges/` | GET |
| `getWorkoutCustomerChargeByCustomerDivision(customerId, divisionId)` | `/workout-charges/customer/{customerId}/division/{divisionId}` | GET |
| `createWorkoutCustomerCharge(data)` | `/workout-charges/` | POST |
| `updateWorkoutCustomerCharge(id, data)` | `/workout-charges/{id}` | PUT |
| `deleteWorkoutCustomerCharge(id)` | `/workout-charges/{id}` | DELETE |
| `getCustomersForDropdown()` | `/workout-charges/customers` | GET |
| `getDivisionsByCustomerId(customerId)` | `/workout-charges/customers/{customerId}/divisions` | GET |
| `getCategoriesForDropdown()` | `/workout-charges/categories` | GET |
| `getCategoryWorkoutsByCategoryId(categoryId)` | `/workout-charges/categories/{categoryId}/workouts` | GET |

### Data Structure Conversion

**TypeScript (Hierarchical):**
```typescript
{
  id: string,
  customer_id: number,
  division_id: number,
  workout_customer_charge_content: {
    category_CAT001: {
      category_id: "CAT001",
      category_name: "Fitness Training",
      workouts: {
        workout_WORKOUT_FIT_001: {
          group_id: "WORKOUT_FIT_001",
          group_name: "Basic Fitness Program",
          unit_price: 50000,
          years: {
            "2024": {
              month_1: {month: 1, quantity: 10},
              month_2: {month: 2, quantity: 12}
            }
          }
        }
      }
    }
  }
}
```

**Database (Flat):**
```sql
workout_customer_charge (
  id: "WCC_123_abc",
  customer_id: 1,
  division_id: 1,
  category_id: "CAT001",
  workout_id: "WORKOUT_FIT_001",
  year: 2024,
  month_1: 10,
  month_2: 12,
  month_3: NULL,
  ...
)
```

### Database Schema

```sql
workout_customer_charge (
  id TEXT PRIMARY KEY,
  customer_id INTEGER NOT NULL,
  division_id INTEGER NULL,
  category_id TEXT NOT NULL,
  workout_id TEXT NOT NULL,
  year INTEGER NOT NULL,
  month_1 to month_12 INTEGER NULL,
  created_at, updated_at TIMESTAMP
)

categories (
  id TEXT PRIMARY KEY,
  name VARCHAR(255),
  description TEXT,
  status VARCHAR(20)
)

workout (
  id TEXT PRIMARY KEY,
  name VARCHAR(255),
  category_id TEXT,
  unit_price DECIMAL(15,2),
  calculation_type VARCHAR(50),
  status VARCHAR(20)
)
```

## 🔒 Security

- Tất cả endpoints đều yêu cầu JWT authentication
- Validation đầy đủ cho complex hierarchical data structure
- SQL injection protection thông qua parameterized queries
- Business logic validation cho data integrity

## 🎯 Features

- ✅ Complex hierarchical data structure support
- ✅ Automatic conversion between flat DB và hierarchical API
- ✅ UPSERT logic (delete + insert for updates)
- ✅ Dropdown data integration với existing tables
- ✅ Fallback mock data khi tables không tồn tại
- ✅ Dynamic category/workout name population
- ✅ Support null division_id
- ✅ Comprehensive validation
- ✅ Auto-generated unique IDs
- ✅ Full CRUD operations
- ✅ Filter support cho list endpoints

## 🔧 Troubleshooting

### Lỗi Data Structure
- Kiểm tra format của `workout_customer_charge_content`
- Đảm bảo có ít nhất 1 category, 1 workout, 1 year, 1 month với quantity > 0

### Lỗi Database Tables
- Chạy `database/workout_charge_tables.sql` để tạo tables
- API sẽ fallback sang mock data nếu categories/workout tables không tồn tại

### Lỗi Foreign Key
```
ERROR: insert or update on table "workout_customer_charge" violates foreign key constraint
```

**Giải pháp:**
1. **Tạo customer tables trước:**
   ```sql
   psql -d your_database -f database/customer_tables.sql
   ```

2. **Kiểm tra customers và divisions có tồn tại:**
   ```sql
   SELECT id, name FROM customers WHERE status = 'active';
   SELECT id, name, customer_id FROM customer_divisions WHERE status = 'active';
   ```

3. **Sử dụng automatic setup script:**
   ```bash
   python setup_workout_charge_api.py
   ```

4. **Hoặc chạy workout charge tables sau customer tables:**
   ```sql
   -- Đảm bảo thứ tự đúng
   \i database/customer_tables.sql
   \i database/workout_charge_tables.sql
   ```

## 📝 Notes

- API này follow pattern của source code hiện tại
- Sử dụng raw SQL queries thay vì ORM
- Database-first approach với auto-population
- Complex data structure validation
- Support cho PostgreSQL với fallback logic
- Tích hợp hoàn toàn với Customer API đã có
