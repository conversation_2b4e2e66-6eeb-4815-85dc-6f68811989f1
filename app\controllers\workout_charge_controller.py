from flask import Blueprint, request, jsonify
from ..services.workout_charge_service import WorkoutChargeService
from ..middleware.auth_middleware import jwt_required_custom

workout_charge_bp = Blueprint('workout_charge', __name__)
workout_charge_service = WorkoutChargeService()

# Main workout charge endpoints
@workout_charge_bp.route('/', methods=['GET'], strict_slashes=False)
@jwt_required_custom
def get_workout_charges():
    """Get all workout customer charges with optional filters"""
    try:
        filters = {}
        
        # Get filters from query parameters
        if request.args.get('customer_id'):
            filters['customer_id'] = int(request.args.get('customer_id'))
        if request.args.get('division_id'):
            filters['division_id'] = int(request.args.get('division_id'))
        
        charges = workout_charge_service.get_workout_charges(filters)
        return jsonify({
            "status": "success",
            "data": charges
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@workout_charge_bp.route('/<charge_id>', methods=['GET'])
@jwt_required_custom
def get_workout_charge(charge_id):
    """Get workout customer charge by ID"""
    try:
        charge = workout_charge_service.get_workout_charge_by_id(charge_id)
        if charge:
            return jsonify({
                "status": "success",
                "data": charge
            }), 200
        return jsonify({
            "status": "error",
            "message": "Workout customer charge not found"
        }), 404
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@workout_charge_bp.route('/customer/<int:customer_id>/division/<int:division_id>', methods=['GET'])
@jwt_required_custom
def get_workout_charge_by_customer_division(customer_id, division_id):
    """Get workout customer charge by customer and division"""
    try:
        # Handle null division_id
        division_id = division_id if division_id != 0 else None
        
        charge = workout_charge_service.get_workout_charge_by_customer_division(customer_id, division_id)
        if charge:
            return jsonify({
                "status": "success",
                "data": charge
            }), 200
        return jsonify({
            "status": "error",
            "message": "Workout customer charge not found"
        }), 404
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@workout_charge_bp.route('/customer/<int:customer_id>/division/null', methods=['GET'])
@jwt_required_custom
def get_workout_charge_by_customer_no_division(customer_id):
    """Get workout customer charge by customer with no division (null)"""
    try:
        charge = workout_charge_service.get_workout_charge_by_customer_division(customer_id, None)
        if charge:
            return jsonify({
                "status": "success",
                "data": charge
            }), 200
        return jsonify({
            "status": "error",
            "message": "Workout customer charge not found"
        }), 404
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@workout_charge_bp.route('/customer/<int:customer_id>/division/0', methods=['GET'])
@jwt_required_custom
def get_workout_charge_by_customer_division_zero(customer_id):
    """Get workout customer charge by customer with division_id = 0 (treated as null)"""
    try:
        charge = workout_charge_service.get_workout_charge_by_customer_division(customer_id, None)
        if charge:
            return jsonify({
                "status": "success",
                "data": charge
            }), 200
        return jsonify({
            "status": "error",
            "message": "Workout customer charge not found"
        }), 404
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@workout_charge_bp.route('/', methods=['POST'])
@jwt_required_custom
def create_workout_charge():
    """Create new workout customer charge (append to existing data)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "No data provided"
            }), 400

        result = workout_charge_service.create_workout_charge(data)
        if result:
            # Get the created charge to return
            created_charge = workout_charge_service.get_workout_charge_by_customer_division(
                data['customer_id'],
                data.get('division_id')
            )
            return jsonify({
                "status": "success",
                "message": "Workout customer charge created successfully",
                "data": created_charge
            }), 201
        else:
            return jsonify({
                "status": "error",
                "message": "Failed to create workout customer charge"
            }), 500
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to create workout customer charge"
        }), 500

@workout_charge_bp.route('/replace', methods=['POST'])
@jwt_required_custom
def replace_workout_charge():
    """Replace all workout customer charge data for a customer/division (UPSERT)"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "No data provided"
            }), 400

        result = workout_charge_service.replace_workout_charge(data)
        if result:
            # Get the replaced charge to return
            replaced_charge = workout_charge_service.get_workout_charge_by_customer_division(
                data['customer_id'],
                data.get('division_id')
            )
            return jsonify({
                "status": "success",
                "message": "Workout customer charge replaced successfully",
                "data": replaced_charge
            }), 200
        else:
            return jsonify({
                "status": "error",
                "message": "Failed to replace workout customer charge"
            }), 500
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to replace workout customer charge"
        }), 500

@workout_charge_bp.route('/<charge_id>', methods=['PUT'])
@jwt_required_custom
def update_workout_charge(charge_id):
    """Update workout customer charge"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "No data provided"
            }), 400
        
        result = workout_charge_service.update_workout_charge(charge_id, data)
        if result:
            # Get the updated charge to return
            updated_charge = workout_charge_service.get_workout_charge_by_customer_division(
                data['customer_id'], 
                data.get('division_id')
            )
            return jsonify({
                "status": "success",
                "message": "Workout customer charge updated successfully",
                "data": updated_charge
            }), 200
        else:
            return jsonify({
                "status": "error",
                "message": "Failed to update workout customer charge"
            }), 500
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to update workout customer charge"
        }), 500

@workout_charge_bp.route('/<charge_id>', methods=['DELETE'])
@jwt_required_custom
def delete_workout_charge(charge_id):
    """Delete workout customer charge"""
    try:
        result = workout_charge_service.delete_workout_charge(charge_id)
        if result:
            return jsonify({
                "status": "success",
                "message": "Workout customer charge deleted successfully"
            }), 200
        else:
            return jsonify({
                "status": "error",
                "message": "Failed to delete workout customer charge"
            }), 500
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to delete workout customer charge"
        }), 500

# Dropdown data endpoints
@workout_charge_bp.route('/customers', methods=['GET'])
@jwt_required_custom
def get_customers_for_dropdown():
    """Get customers for dropdown"""
    try:
        customers = workout_charge_service.get_customers_for_dropdown()
        return jsonify({
            "status": "success",
            "data": customers
        }), 200
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@workout_charge_bp.route('/customers/<int:customer_id>/divisions', methods=['GET'])
@jwt_required_custom
def get_divisions_by_customer_id(customer_id):
    """Get divisions by customer ID for dropdown"""
    try:
        divisions = workout_charge_service.get_divisions_by_customer_id(customer_id)
        return jsonify({
            "status": "success",
            "data": divisions
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@workout_charge_bp.route('/categories', methods=['GET'])
@jwt_required_custom
def get_categories_for_dropdown():
    """Get categories for dropdown"""
    try:
        categories = workout_charge_service.get_categories_for_dropdown()
        return jsonify({
            "status": "success",
            "data": categories
        }), 200
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@workout_charge_bp.route('/categories/', methods=['GET'], strict_slashes=False)
@jwt_required_custom
def get_all_categories():
    """Get all categories with detailed information"""
    try:
        categories = workout_charge_service.get_all_categories()
        return jsonify({
            "status": "success",
            "data": categories,
            "total": len(categories)
        }), 200
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@workout_charge_bp.route('/categories/<category_id>/workouts', methods=['GET'])
@jwt_required_custom
def get_category_workouts_by_category_id(category_id):
    """Get category workouts by category ID for dropdown"""
    try:
        workouts = workout_charge_service.get_category_workouts_by_category_id(category_id)
        return jsonify({
            "status": "success",
            "data": workouts
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500
