# Workout Customer Charge API Documentation

Tài liệu này mô tả các API endpoints cho Workout Customer Charge management được tạo dựa trên các function trong `service_code.ts`.

## Base URL
```
/workout-charges
```

## Authentication
Tất cả các endpoints đều yêu cầu JWT authentication thông qua header:
```
Authorization: Bearer <token>
```

## Main Workout Charge Endpoints

### 1. Get All Workout Charges
**Endpoint:** `GET /workout-charges/`  
**Mô tả:** <PERSON><PERSON><PERSON> danh sách tất cả workout customer charges với optional filters  
**Query Parameters:**
- `customer_id` (optional): Filter by customer ID
- `division_id` (optional): Filter by division ID

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": "WCC_1234567890_abc123def",
      "customer_id": 1,
      "division_id": 1,
      "workout_customer_charge_content": {
        "category_CAT001": {
          "category_id": "CAT001",
          "category_name": "Fitness Training",
          "workouts": {
            "workout_WORKOUT_FIT_001": {
              "group_id": "WORKOUT_FIT_001",
              "group_name": "Basic Fitness Program",
              "unit_price": 50000,
              "years": {
                "2024": {
                  "month_1": {"month": 1, "quantity": 10},
                  "month_2": {"month": 2, "quantity": 12}
                }
              }
            }
          }
        }
      },
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00"
    }
  ]
}
```

### 2. Get Workout Charge by ID
**Endpoint:** `GET /workout-charges/{charge_id}`  
**Mô tả:** Lấy workout customer charge theo ID  
**Response:** Same structure as above for single item

### 3. Get Workout Charge by Customer and Division
**Endpoint:** `GET /workout-charges/customer/{customer_id}/division/{division_id}`  
**Mô tả:** Lấy workout customer charge theo customer và division  
**Note:** Use division_id = 0 for null division  
**Response:** Same structure as above for single item

### 4. Create Workout Charge
**Endpoint:** `POST /workout-charges/`
**Mô tả:** Tạo workout customer charge mới (append to existing data)
**Note:** Sẽ báo lỗi nếu đã tồn tại workout charge cho customer/division này
**Request Body:**
```json
{
  "customer_id": 2,
  "division_id": 3,
  "workout_customer_charge_content": {
    "category_CAT001": {
      "category_id": "CAT001",
      "workouts": {
        "workout_WORKOUT_FIT_001": {
          "group_id": "WORKOUT_FIT_001",
          "years": {
            "2024": {
              "month_1": {"month": 1, "quantity": 10},
              "month_2": {"month": 2, "quantity": 12},
              "month_3": {"month": 3, "quantity": 15}
            }
          }
        }
      }
    }
  }
}
```
**Response:**
```json
{
  "status": "success",
  "message": "Workout customer charge created successfully",
  "data": {
    // Full created object with populated names and prices
  }
}
```

### 5. Replace Workout Charge (UPSERT)
**Endpoint:** `POST /workout-charges/replace`
**Mô tả:** Thay thế toàn bộ workout customer charge data cho customer/division (UPSERT logic)
**Note:** Sẽ xóa tất cả data cũ của customer/division và insert data mới
**Request Body:** Same as create
**Response:**
```json
{
  "status": "success",
  "message": "Workout customer charge replaced successfully",
  "data": {
    // Full replaced object with populated names and prices
  }
}
```

### 6. Update Workout Charge
**Endpoint:** `PUT /workout-charges/{charge_id}`  
**Mô tả:** Cập nhật workout customer charge  
**Request Body:** Same as create  
**Response:**
```json
{
  "status": "success",
  "message": "Workout customer charge updated successfully",
  "data": {
    // Full updated object
  }
}
```

### 6. Delete Workout Charge
**Endpoint:** `DELETE /workout-charges/{charge_id}`  
**Mô tả:** Xóa workout customer charge  
**Response:**
```json
{
  "status": "success",
  "message": "Workout customer charge deleted successfully"
}
```

## Dropdown Data Endpoints

### 7. Get Customers for Dropdown
**Endpoint:** `GET /workout-charges/customers`  
**Mô tả:** Lấy danh sách customers cho dropdown  
**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "ABC Company Ltd",
      "code": "CUST001"
    }
  ]
}
```

### 8. Get Divisions by Customer ID
**Endpoint:** `GET /workout-charges/customers/{customer_id}/divisions`  
**Mô tả:** Lấy danh sách divisions theo customer ID cho dropdown  
**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "Sales Division",
      "code": "DIV001",
      "customer_id": 1
    }
  ]
}
```

### 9. Get Categories for Dropdown
**Endpoint:** `GET /workout-charges/categories`
**Mô tả:** Lấy danh sách categories cho dropdown (simple format)
**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": "CAT001",
      "name": "Fitness Training"
    },
    {
      "id": "CAT002",
      "name": "Strength Training"
    }
  ]
}
```

### 9.1. Get All Categories
**Endpoint:** `GET /workout-charges/categories/`
**Mô tả:** Lấy tất cả categories với thông tin chi tiết
**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": "CAT001",
      "name": "Fitness Training",
      "description": "General fitness and conditioning programs",
      "status": "active",
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00"
    },
    {
      "id": "CAT002",
      "name": "Strength Training",
      "description": "Weight lifting and muscle building programs",
      "status": "active",
      "created_at": "2024-01-01T00:00:00",
      "updated_at": "2024-01-01T00:00:00"
    }
  ],
  "total": 6
}
```

### 10. Get Workouts by Category ID
**Endpoint:** `GET /workout-charges/categories/{category_id}/workouts`  
**Mô tả:** Lấy danh sách workouts theo category ID cho dropdown  
**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": "WORKOUT_FIT_001",
      "name": "Basic Fitness Program",
      "unit_price": 50000,
      "category_id": "CAT001"
    }
  ]
}
```

### 11. Delete Workout Charges by Customer/Division (Soft Delete)
**Endpoint:** `DELETE /workout-charges/customer/{customer_id}/division/{division_id}`
**Mô tả:** Soft delete tất cả workout customer charges theo customer_id và division_id (đánh dấu is_deleted = TRUE)
**Response:**
```json
{
  "status": "success",
  "message": "Deleted 5 workout customer charge records",
  "deleted_count": 5
}
```

### 11.1. Delete Workout Charges by Customer (No Division) (Soft Delete)
**Endpoint:** `DELETE /workout-charges/customer/{customer_id}/division/null`
**Mô tả:** Soft delete tất cả workout customer charges theo customer_id với division_id = null
**Response:**
```json
{
  "status": "success",
  "message": "Deleted 3 workout customer charge records",
  "deleted_count": 3
}
```

### 11.2. Delete Workout Charges by Customer (Division = 0) (Soft Delete)
**Endpoint:** `DELETE /workout-charges/customer/{customer_id}/division/0`
**Mô tả:** Soft delete tất cả workout customer charges theo customer_id với division_id = 0 (treated as null)
**Response:**
```json
{
  "status": "success",
  "message": "Deleted 2 workout customer charge records",
  "deleted_count": 2
}
```

## Data Structure

### Hierarchical Structure
Workout Customer Charge sử dụng cấu trúc hierarchical phức tạp:

```
WorkoutCustomerCharge
├── id (string)
├── customer_id (number)
├── division_id (number|null)
├── workout_customer_charge_content (object)
│   └── category_{category_id} (object)
│       ├── category_id (string)
│       ├── category_name (string)
│       └── workouts (object)
│           └── workout_{workout_id} (object)
│               ├── group_id (string)
│               ├── group_name (string)
│               ├── unit_price (number)
│               └── years (object)
│                   └── {year} (object)
│                       └── month_{1-12} (object)
│                           ├── month (number)
│                           └── quantity (number)
├── created_at (string)
└── updated_at (string)
```

### Flat Database Structure
Trong database, data được lưu dưới dạng flat:

```sql
workout_customer_charge (
  id TEXT,
  customer_id INTEGER,
  division_id INTEGER,
  category_id TEXT,
  workout_id TEXT,
  year INTEGER,
  month_1 INTEGER, month_2 INTEGER, ..., month_12 INTEGER,
  is_deleted BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP,
  updated_at TIMESTAMP
)
```

## Error Responses

### 400 Bad Request
```json
{
  "status": "error",
  "message": "Customer ID is required"
}
```

### 404 Not Found
```json
{
  "status": "error",
  "message": "Workout customer charge not found"
}
```

### 500 Internal Server Error
```json
{
  "status": "error",
  "message": "Failed to create workout customer charge"
}
```

## Database Tables

API này sử dụng các bảng sau trong PostgreSQL:

1. **workout_customer_charge** - Lưu data workout charge (flat structure) + `is_deleted` column
2. **categories** - Lưu thông tin categories
3. **workout** - Lưu thông tin workout programs
4. **customers** - Reference từ Customer API + `is_deleted` column
5. **customer_divisions** - Reference từ Customer API + `is_deleted` column
6. **customer_settings** - Customer settings + `is_deleted` column

**Soft Delete Tables**: Các bảng có cột `is_deleted` để thực hiện soft delete:
- `workout_customer_charge.is_deleted`
- `customers.is_deleted`
- `customer_divisions.is_deleted`
- `customer_settings.is_deleted`

## Business Logic

- **UPSERT Logic**: Create/Update sẽ soft delete data cũ và insert data mới cho customer/division
- **Soft Delete**: Sử dụng cột `is_deleted` để đánh dấu records đã xóa thay vì xóa vật lý
- **Data Filtering**: Tất cả queries tự động filter `is_deleted = FALSE` để chỉ lấy active records
- **Hierarchical Conversion**: Tự động convert giữa flat database structure và hierarchical API structure
- **Validation**: Validate structure phức tạp của workout_customer_charge_content
- **Dropdown Integration**: Tích hợp với existing customer và division data (chỉ active records)

## Notes

- API này follow pattern của source code hiện tại
- Sử dụng raw SQL queries thông qua DAO layer
- Support cả PostgreSQL và fallback mock data
- Tự động populate category names và workout info từ database
- ID generation sử dụng timestamp + random string
- Support null division_id cho customer-level charges
