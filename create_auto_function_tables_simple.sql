-- Simple SQL script to create auto_function tables without seed data
-- Use this if you want to create tables first and add data later via API

-- Create auto_function table
CREATE TABLE auto_function (
    id SERIAL PRIMARY KEY,
    code VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(500) NOT NULL,
    note TEXT,
    is_deleted B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create auto_function_customers junction table
CREATE TABLE auto_function_customers (
    id SERIAL PRIMARY KEY,
    auto_function_id INTEGER NOT NULL,
    customer_id INTEGER NOT NULL,
    is_deleted BO<PERSON>EAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraints
    CONSTRAINT fk_auto_function_customers_auto_function 
        FOREIGN KEY (auto_function_id) REFERENCES auto_function(id) ON DELETE CASCADE,
    CONSTRAINT fk_auto_function_customers_customer 
        FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent duplicate relationships
    CONSTRAINT uk_auto_function_customers_unique 
        UNIQUE(auto_function_id, customer_id)
);

-- Create indexes for better performance
CREATE INDEX idx_auto_function_code ON auto_function(code);
CREATE INDEX idx_auto_function_name ON auto_function(name);
CREATE INDEX idx_auto_function_is_deleted ON auto_function(is_deleted);
CREATE INDEX idx_auto_function_created_at ON auto_function(created_at);

-- Indexes for auto_function_customers table
CREATE INDEX idx_auto_function_customers_auto_function_id ON auto_function_customers(auto_function_id);
CREATE INDEX idx_auto_function_customers_customer_id ON auto_function_customers(customer_id);
CREATE INDEX idx_auto_function_customers_is_deleted ON auto_function_customers(is_deleted);

-- Composite indexes for common query patterns
CREATE INDEX idx_auto_function_customers_function_is_deleted 
    ON auto_function_customers(auto_function_id, is_deleted);
CREATE INDEX idx_auto_function_customers_customer_is_deleted 
    ON auto_function_customers(customer_id, is_deleted);

-- Create trigger function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to auto_function table
CREATE TRIGGER update_auto_function_updated_at 
    BEFORE UPDATE ON auto_function 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Apply trigger to auto_function_customers table
CREATE TRIGGER update_auto_function_customers_updated_at 
    BEFORE UPDATE ON auto_function_customers 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE auto_function IS 'Auto function definitions for automated calculations';
COMMENT ON COLUMN auto_function.id IS 'Primary key - auto increment';
COMMENT ON COLUMN auto_function.code IS 'Unique code identifier for the auto function';
COMMENT ON COLUMN auto_function.name IS 'Display name of the auto function';
COMMENT ON COLUMN auto_function.note IS 'Additional notes or description';
COMMENT ON COLUMN auto_function.is_deleted IS 'Soft delete flag - TRUE means record is deleted';
COMMENT ON COLUMN auto_function.created_at IS 'Timestamp when record was created';
COMMENT ON COLUMN auto_function.updated_at IS 'Timestamp when record was last updated';

COMMENT ON TABLE auto_function_customers IS 'Junction table linking auto functions to customers';
COMMENT ON COLUMN auto_function_customers.id IS 'Primary key - auto increment';
COMMENT ON COLUMN auto_function_customers.auto_function_id IS 'Foreign key to auto_function table';
COMMENT ON COLUMN auto_function_customers.customer_id IS 'Foreign key to customers table';
COMMENT ON COLUMN auto_function_customers.is_deleted IS 'Soft delete flag - TRUE means relationship is deleted';
COMMENT ON COLUMN auto_function_customers.created_at IS 'Timestamp when relationship was created';
COMMENT ON COLUMN auto_function_customers.updated_at IS 'Timestamp when relationship was last updated';

-- Verify the tables were created successfully
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('auto_function', 'auto_function_customers')
ORDER BY table_name, ordinal_position;
