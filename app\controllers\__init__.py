from .auth_controller import auth_bp
from .user_controller import user_bp
from .auto_function_controller import auto_function_bp
from .unit_controller import unit_bp
from .customers_controller import customer_bp
from .workout_charge_controller import workout_charge_bp

def register_blueprints(app):
    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(user_bp, url_prefix='/user')
    app.register_blueprint(auto_function_bp, url_prefix='/auto-function')
    app.register_blueprint(unit_bp, url_prefix='/unit')
    app.register_blueprint(customer_bp, url_prefix='/customers')
    app.register_blueprint(workout_charge_bp, url_prefix='/workout-charges')
