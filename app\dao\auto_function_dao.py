from sqlalchemy import create_engine, text
from config import Config

class AutoFunctionDAO:
    def __init__(self):
        self.engine = create_engine(Config.get_db_uri())

    def get_all(self, filters=None):
        # Xây dựng base query
        base_sql = """
            SELECT af.id, af.code, af.name, af.note,
                   af.created_at, af.updated_at
            FROM auto_function af
            WHERE af.is_deleted = FALSE
        """
        
        params = {}
        conditions = []

        # Thêm điều kiện search nếu có
        if filters and filters.get('search'):
            search_term = f"%{filters['search']}%"
            conditions.append("(af.code LIKE :search OR af.name LIKE :search OR af.note LIKE :search)")
            params['search'] = search_term

        # Thêm điều kiện customer_id nếu có
        if filters and filters.get('customer_id'):
            conditions.append("""
                af.id IN (
                    SELECT auto_function_id
                    FROM auto_function_customers
                    WHERE customer_id = :customer_id AND is_deleted = FALSE
                )
            """)
            params['customer_id'] = filters['customer_id']

        # <PERSON><PERSON><PERSON> hợ<PERSON> các điều kiện
        if conditions:
            base_sql += " AND " + " AND ".join(conditions)

        base_sql += " ORDER BY af.id ASC"

        with self.engine.connect() as conn:
            result = conn.execute(text(base_sql), params)
            auto_functions = []
            for row in result:
                auto_functions.append({
                    'id': row.id,
                    'code': row.code,
                    'name': row.name,
                    'note': row.note,
                    'created_at': row.created_at.isoformat() if row.created_at else None,
                    'updated_at': row.updated_at.isoformat() if row.updated_at else None
                })

            # Lấy customers cho mỗi auto function
            for af in auto_functions:
                customer_sql = """
                    SELECT c.id, c.name
                    FROM customers c
                    JOIN auto_function_customers afc ON c.id = afc.customer_id
                    WHERE afc.auto_function_id = :af_id
                    AND afc.is_deleted = FALSE
                    AND c.is_deleted = FALSE
                    ORDER BY c.name
                """
                customer_result = conn.execute(text(customer_sql), {'af_id': af['id']})
                af['customer_ids'] = []
                af['customer_names'] = []
                for c_row in customer_result:
                    af['customer_ids'].append(c_row.id)
                    af['customer_names'].append(c_row.name)

            return auto_functions

    def get_by_id(self, id):
        sql = """
            SELECT id, code, name, note, created_at, updated_at
            FROM auto_function
            WHERE id = :id AND is_deleted = FALSE
        """
        
        with self.engine.connect() as conn:
            result = conn.execute(text(sql), {'id': id})
            row = result.fetchone()
            
            if not row:
                return None

            auto_function = {
                'id': row.id,
                'code': row.code,
                'name': row.name,
                'note': row.note,
                'created_at': row.created_at.isoformat() if row.created_at else None,
                'updated_at': row.updated_at.isoformat() if row.updated_at else None
            }

            # Get customers
            customer_sql = """
                SELECT c.id, c.name
                FROM customers c
                JOIN auto_function_customers afc ON c.id = afc.customer_id
                WHERE afc.auto_function_id = :af_id
                AND afc.is_deleted = FALSE
                AND c.is_deleted = FALSE
                ORDER BY c.name
            """
            customer_result = conn.execute(text(customer_sql), {'af_id': id})
            auto_function['customer_ids'] = []
            auto_function['customer_names'] = []
            for c_row in customer_result:
                auto_function['customer_ids'].append(c_row.id)
                auto_function['customer_names'].append(c_row.name)

            return auto_function

    def create(self, data):
        with self.engine.connect() as conn:
            with conn.begin():  # Start transaction
                # Insert auto function
                insert_sql = """
                    INSERT INTO auto_function (code, name, note, created_at, updated_at, is_deleted)
                    VALUES (:code, :name, :note, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, FALSE)
                    RETURNING id
                """
                result = conn.execute(text(insert_sql), {
                    'code': data['code'],
                    'name': data['name'],
                    'note': data.get('note', '')
                })
                new_id = result.fetchone()[0]

                # Insert customer relationships
                if data.get('customer_ids'):
                    customer_sql = """
                        INSERT INTO auto_function_customers (auto_function_id, customer_id, created_at, is_deleted)
                        VALUES (:af_id, :customer_id, CURRENT_TIMESTAMP, FALSE)
                    """
                    for customer_id in data['customer_ids']:
                        conn.execute(text(customer_sql), {
                            'af_id': new_id,
                            'customer_id': customer_id
                        })

                return new_id

    def update(self, id, data):
        with self.engine.connect() as conn:
            with conn.begin():  # Start transaction
                # Update auto function
                update_sql = """
                    UPDATE auto_function
                    SET code = :code,
                        name = :name,
                        note = :note,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id AND is_deleted = FALSE
                """
                conn.execute(text(update_sql), {
                    'id': id,
                    'code': data['code'],
                    'name': data['name'],
                    'note': data.get('note', '')
                })

                # Update customer relationships
                # First soft delete existing relationships
                conn.execute(text("""
                    UPDATE auto_function_customers
                    SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP
                    WHERE auto_function_id = :af_id AND is_deleted = FALSE
                """), {'af_id': id})

                # Then insert new relationships
                if data.get('customer_ids'):
                    customer_sql = """
                        INSERT INTO auto_function_customers (auto_function_id, customer_id, created_at, is_deleted)
                        VALUES (:af_id, :customer_id, CURRENT_TIMESTAMP, FALSE)
                    """
                    for customer_id in data['customer_ids']:
                        conn.execute(text(customer_sql), {
                            'af_id': id,
                            'customer_id': customer_id
                        })

                return True

    def delete(self, id):
        """Soft delete auto function and its customer relationships"""
        with self.engine.connect() as conn:
            with conn.begin():
                # Soft delete customer relationships first
                conn.execute(text("""
                    UPDATE auto_function_customers
                    SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP
                    WHERE auto_function_id = :id AND is_deleted = FALSE
                """), {'id': id})

                # Then soft delete the auto function
                result = conn.execute(text("""
                    UPDATE auto_function
                    SET is_deleted = TRUE, updated_at = CURRENT_TIMESTAMP
                    WHERE id = :id AND is_deleted = FALSE
                """), {'id': id})

                return result.rowcount > 0

    def seed_auto_focus_data(self):
        with self.engine.connect() as conn:
            with conn.begin():  # Start transaction
                # Check if data already exists
                check_sql = 'SELECT COUNT(*) as count FROM auto_function'
                count = conn.execute(text(check_sql)).scalar()
                if count > 0:
                    return False                # Find Nitori customer ID
                nitori_result = conn.execute(text(
                    'SELECT id FROM customers WHERE code = :code OR name = :name'
                ), {
                    'code': 'CUS002',
                    'name': 'Nitori'
                })
                nitori_id = nitori_result.fetchone()
                if not nitori_id:
                    # If no Nitori customer, create auto functions without customer link
                    nitori_id = None
                else:
                    nitori_id = nitori_id[0]

                # Initial auto functions data
                auto_functions = [
                    {
                        'code': 'N_A_IN_SUM_CARTON',
                        'name': 'Nitory Inbound Auto Calculation Total Cartons Received',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_IN_SUM_PCS',
                        'name': 'Nitory Inbound Auto Calculation Total Pieces Received',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_IN_SUM_AI',
                        'name': 'Nitory Inbound Auto  Calculation Total AI Pieces',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_IN_SUM_M3',
                        'name': 'Nitory Inbound Auto Calculation  Total m3',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_ALL_SUM_CARTON',
                        'name': 'Nitory Outbound Auto  Calculation Total Cartons Received',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_ALL_SUM_PCS',
                        'name': 'Nitory Outbound Auto  Calculation Total Pieces Received',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_ALL_SUM_M3',
                        'name': 'Nitory Outbound Auto Calculation  Total m3',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_STORE_SUM_CARTON',
                        'name': 'Nitory Outbound Auto Calculation Total Cartons Received for Store',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_STORE_SUM_PCS',
                        'name': 'Nitory Outbound Auto Calculation Total Pieces Received for Store',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_STORE_SUM_M3',
                        'name': 'Nitory Outbound Auto Calculation  Total m3 for Store',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_CUSTOMER_SUM_CARTON',
                        'name': 'Nitory Outbound Auto Calculation Total Cartons Received for Customer',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_CUSTOMER_SUM_PCS',
                        'name': 'Nitory Outbound Auto Calculation Total Pieces Received for Customer',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_CUSTOMER_SUM_M3',
                        'name': 'Nitory Outbound Auto Calculation  Total m3 for Customer',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_CUSTOMER_SUM_LABEL',
                        'name': 'Nitory Outbound Auto Calculation Total Labeling',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_NONE_SUM_CARTON',
                        'name': 'Nitory Outbound Auto  Calculation Total Cartons without COD',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_NONE_SUM_PCS',
                        'name': 'Nitory Outbound Auto  Calculation Total Pieces without COD',
                        'note': 'For Nitory Only'
                    },
                    {
                        'code': 'N_A_OUT_NONE_SUM_M3',
                        'name': 'Nitory Outbound Auto Calculation Total m3 without COD',
                        'note': 'For Nitory Only'
                    }
                ]

                # Insert auto functions
                insert_sql = """
                    INSERT INTO auto_function (code, name, note, created_at, updated_at, is_deleted)
                    VALUES (:code, :name, :note, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, FALSE)
                    RETURNING id
                """

                # Link with Nitori customer if found
                if nitori_id:
                    customer_sql = """
                        INSERT INTO auto_function_customers (auto_function_id, customer_id, created_at, is_deleted)
                        VALUES (:af_id, :customer_id, CURRENT_TIMESTAMP, FALSE)
                    """

                for af in auto_functions:
                    # Insert auto function
                    result = conn.execute(text(insert_sql), af)
                    af_id = result.fetchone()[0]

                    # Link with Nitori if found
                    if nitori_id:
                        conn.execute(text(customer_sql), {
                            'af_id': af_id,
                            'customer_id': nitori_id
                        })

                return True

    def get_customers_for_dropdown(self):
        """Get customers for dropdown (excluding soft deleted)"""
        try:
            with self.engine.connect() as conn:
                query = """
                    SELECT id, name, code
                    FROM customers
                    WHERE is_deleted = FALSE AND status = 'active'
                    ORDER BY name
                """

                result = conn.execute(text(query))
                customers = []

                for row in result:
                    customers.append({
                        'id': row.id,
                        'name': row.name,
                        'code': row.code
                    })

                return customers

        except Exception as e:
            print(f"Error getting customers for dropdown: {str(e)}")
            raise e
