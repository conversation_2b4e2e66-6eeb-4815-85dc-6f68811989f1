from flask import Blueprint, request, jsonify
from ..services.customer_service import CustomerService
from ..middleware.auth_middleware import jwt_required_custom

customer_bp = Blueprint('customers', __name__)
customer_service = CustomerService()

# Customer endpoints
@customer_bp.route('/', methods=['GET'], strict_slashes=False)
@jwt_required_custom
def get_all_customers():
    try:
        customers = customer_service.get_all_customers()
        return jsonify({
            "status": "success",
            "data": customers
        }), 200
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@customer_bp.route('/<int:customer_id>', methods=['GET'])
@jwt_required_custom
def get_customer(customer_id):
    try:
        customer = customer_service.get_customer_by_id(customer_id)
        if customer:
            return jsonify({
                "status": "success",
                "data": customer
            }), 200
        return jsonify({
            "status": "error",
            "message": "Customer not found"
        }), 404
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@customer_bp.route('/', methods=['POST'])
@jwt_required_custom
def create_customer():
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "No data provided"
            }), 400

        customer_id = customer_service.add_customer(data)
        return jsonify({
            "status": "success",
            "message": "Customer created successfully",
            "data": {"id": customer_id}
        }), 201
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to create customer"
        }), 500

@customer_bp.route('/<int:customer_id>', methods=['PUT'])
@jwt_required_custom
def update_customer(customer_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "No data provided"
            }), 400

        data['id'] = customer_id
        customer_service.update_customer(data)
        return jsonify({
            "status": "success",
            "message": "Customer updated successfully"
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to update customer"
        }), 500

@customer_bp.route('/<int:customer_id>', methods=['DELETE'])
@jwt_required_custom
def delete_customer(customer_id):
    try:
        customer_service.delete_customer(customer_id)
        return jsonify({
            "status": "success",
            "message": "Customer deleted successfully (soft delete)"
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to delete customer"
        }), 500

# Customer Settings endpoints
@customer_bp.route('/<int:customer_id>/settings', methods=['GET'])
@jwt_required_custom
def get_customer_settings(customer_id):
    try:
        settings = customer_service.get_customer_settings(customer_id)
        if settings:
            return jsonify({
                "status": "success",
                "data": settings
            }), 200
        return jsonify({
            "status": "error",
            "message": "Customer settings not found"
        }), 404
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@customer_bp.route('/<int:customer_id>/settings', methods=['POST'])
@jwt_required_custom
def add_or_update_customer_settings(customer_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "No data provided"
            }), 400

        data['customerId'] = customer_id
        customer_service.add_or_update_customer_settings(data)
        return jsonify({
            "status": "success",
            "message": "Customer settings saved successfully"
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to save customer settings"
        }), 500

@customer_bp.route('/<int:customer_id>/settings', methods=['DELETE'])
@jwt_required_custom
def delete_customer_settings(customer_id):
    try:
        customer_service.delete_customer_settings(customer_id)
        return jsonify({
            "status": "success",
            "message": "Customer settings deleted successfully (soft delete)"
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to delete customer settings"
        }), 500

# Customer Divisions endpoints
@customer_bp.route('/<int:customer_id>/divisions', methods=['GET'])
@jwt_required_custom
def get_customer_divisions(customer_id):
    try:
        divisions = customer_service.get_customer_divisions_by_customer_id(customer_id)
        return jsonify({
            "status": "success",
            "data": divisions
        }), 200
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@customer_bp.route('/divisions/<int:division_id>', methods=['GET'])
@jwt_required_custom
def get_customer_division(division_id):
    try:
        division = customer_service.get_customer_division_by_id(division_id)
        if division:
            return jsonify({
                "status": "success",
                "data": division
            }), 200
        return jsonify({
            "status": "error",
            "message": "Customer division not found"
        }), 404
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500

@customer_bp.route('/<int:customer_id>/divisions', methods=['POST'])
@jwt_required_custom
def create_customer_division(customer_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "No data provided"
            }), 400

        data['customerId'] = customer_id
        division_id = customer_service.add_customer_division(data)
        return jsonify({
            "status": "success",
            "message": "Customer division created successfully",
            "data": {"id": division_id}
        }), 201
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to create customer division"
        }), 500

@customer_bp.route('/divisions/<int:division_id>', methods=['PUT'])
@jwt_required_custom
def update_customer_division(division_id):
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                "status": "error",
                "message": "No data provided"
            }), 400

        data['id'] = division_id
        customer_service.update_customer_division(data)
        return jsonify({
            "status": "success",
            "message": "Customer division updated successfully"
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to update customer division"
        }), 500

@customer_bp.route('/divisions/<int:division_id>', methods=['DELETE'])
@jwt_required_custom
def delete_customer_division(division_id):
    try:
        customer_service.delete_customer_division(division_id)
        return jsonify({
            "status": "success",
            "message": "Customer division deleted successfully (soft delete)"
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to delete customer division"
        }), 500
