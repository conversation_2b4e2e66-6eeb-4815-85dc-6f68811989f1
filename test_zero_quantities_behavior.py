#!/usr/bin/env python3
"""
Test script to verify that years with all zero quantities are saved to database
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:5000"
AUTH_ENDPOINT = f"{BASE_URL}/auth/login"
WORKOUT_CHARGE_ENDPOINT = f"{BASE_URL}/workout-charges"

# Test credentials
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

def authenticate():
    """Get JWT token for authentication"""
    print("🔐 Authenticating...")
    try:
        response = requests.post(AUTH_ENDPOINT, json=TEST_CREDENTIALS)
        if response.status_code == 200:
            data = response.json()
            token = data.get('data', {}).get('access_token')
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            print("✅ Authentication successful")
            return headers
        else:
            print(f"❌ Authentication failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return None

def test_zero_quantities_saved():
    """Test that years with all zero quantities are saved to database"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n🧪 Testing Zero Quantities Behavior...")
    
    # Payload with mix of zero and positive quantities
    payload = {
        "customer_id": 1,
        "division_id": 4,  # Use unique division for this test
        "workout_customer_charge_content": {
            "category_CAT001": {
                "category_id": "CAT001",
                "workouts": {
                    "workout_WORKOUT_FIT_001": {
                        "group_id": "WORKOUT_FIT_001",
                        "years": {
                            "2024": {
                                "month_1": {"month": 1, "quantity": 0},
                                "month_2": {"month": 2, "quantity": 0},
                                "month_3": {"month": 3, "quantity": 0}
                            },
                            "2025": {
                                "month_1": {"month": 1, "quantity": 10},
                                "month_2": {"month": 2, "quantity": 20}
                            },
                            "2026": {
                                "month_1": {"month": 1, "quantity": 0},
                                "month_2": {"month": 2, "quantity": 0},
                                "month_3": {"month": 3, "quantity": 0},
                                "month_4": {"month": 4, "quantity": 0}
                            },
                            "2027": {}  # Empty year - should be ignored
                        }
                    }
                }
            }
        }
    }
    
    print("📝 Testing REPLACE with zero quantities...")
    try:
        response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/replace", 
                               json=payload, 
                               headers=headers)
        
        if response.status_code == 200:
            print("✅ REPLACE with zero quantities succeeded!")
            
            # Verify the data was saved correctly
            print("🔍 Verifying saved data...")
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/customer/1/division/4", 
                                  headers=headers)
            if response.status_code == 200:
                saved_data = response.json().get('data', {})
                content = saved_data.get('workout_customer_charge_content', {})
                
                if 'category_CAT001' in content:
                    workouts = content['category_CAT001'].get('workouts', {})
                    if 'workout_WORKOUT_FIT_001' in workouts:
                        years = workouts['workout_WORKOUT_FIT_001'].get('years', {})
                        print(f"   Saved years: {list(years.keys())}")
                        
                        # Check specific years
                        checks = [
                            ("2024", "all zero quantities", True),
                            ("2025", "positive quantities", True),
                            ("2026", "all zero quantities", True),
                            ("2027", "empty year", False)
                        ]
                        
                        for year, description, should_exist in checks:
                            if should_exist:
                                if year in years:
                                    print(f"   ✅ Year {year} ({description}) correctly saved")
                                    
                                    # Check month data for zero quantity years
                                    if year in ["2024", "2026"]:
                                        year_data = years[year]
                                        zero_months = []
                                        for month_key, month_data in year_data.items():
                                            if month_key.startswith('month_') and month_data.get('quantity') == 0:
                                                zero_months.append(month_key)
                                        
                                        if zero_months:
                                            print(f"      📊 Zero quantity months saved: {zero_months}")
                                        else:
                                            print(f"      ⚠️ No zero quantity months found")
                                    
                                else:
                                    print(f"   ❌ Year {year} ({description}) missing from saved data")
                            else:
                                if year not in years:
                                    print(f"   ✅ Year {year} ({description}) correctly excluded")
                                else:
                                    print(f"   ⚠️ Year {year} ({description}) unexpectedly saved")
                        
                        return True
                    else:
                        print("❌ Workout not found in saved data")
                else:
                    print("❌ Category not found in saved data")
            else:
                print(f"❌ Failed to retrieve saved data: {response.text}")
            
            return False
        else:
            print(f"❌ REPLACE with zero quantities failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        return False

def test_all_zero_quantities_year():
    """Test a year with only zero quantities"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n🧪 Testing Year with All Zero Quantities...")
    
    payload = {
        "customer_id": 1,
        "division_id": 5,  # Use unique division
        "workout_customer_charge_content": {
            "category_CAT002": {
                "category_id": "CAT002",
                "workouts": {
                    "workout_WORKOUT_STR_001": {
                        "group_id": "WORKOUT_STR_001",
                        "years": {
                            "2024": {
                                "month_1": {"month": 1, "quantity": 0},
                                "month_2": {"month": 2, "quantity": 0},
                                "month_3": {"month": 3, "quantity": 0},
                                "month_4": {"month": 4, "quantity": 0},
                                "month_5": {"month": 5, "quantity": 0},
                                "month_6": {"month": 6, "quantity": 0},
                                "month_7": {"month": 7, "quantity": 0},
                                "month_8": {"month": 8, "quantity": 0},
                                "month_9": {"month": 9, "quantity": 0},
                                "month_10": {"month": 10, "quantity": 0},
                                "month_11": {"month": 11, "quantity": 0},
                                "month_12": {"month": 12, "quantity": 0}
                            }
                        }
                    }
                }
            }
        }
    }
    
    print("📝 Testing year with all zero quantities...")
    try:
        response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/replace", 
                               json=payload, 
                               headers=headers)
        
        if response.status_code == 200:
            print("✅ Year with all zero quantities accepted!")
            
            # Verify it was saved
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/customer/1/division/5", 
                                  headers=headers)
            if response.status_code == 200:
                saved_data = response.json().get('data', {})
                content = saved_data.get('workout_customer_charge_content', {})
                
                if 'category_CAT002' in content:
                    workouts = content['category_CAT002'].get('workouts', {})
                    if 'workout_WORKOUT_STR_001' in workouts:
                        years = workouts['workout_WORKOUT_STR_001'].get('years', {})
                        
                        if '2024' in years:
                            print("✅ Year 2024 with all zero quantities correctly saved")
                            
                            # Count zero quantity months
                            year_data = years['2024']
                            zero_count = 0
                            for month_key, month_data in year_data.items():
                                if month_key.startswith('month_') and month_data.get('quantity') == 0:
                                    zero_count += 1
                            
                            print(f"   📊 Saved {zero_count} months with zero quantities")
                            return True
                        else:
                            print("❌ Year 2024 not found in saved data")
                    else:
                        print("❌ Workout not found")
                else:
                    print("❌ Category not found")
            else:
                print(f"❌ Failed to retrieve data: {response.text}")
            
            return False
        else:
            print(f"❌ Year with all zero quantities failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        return False

def test_behavior_comparison():
    """Test and compare different scenarios"""
    print("\n📋 Behavior Comparison Summary:")
    print("=" * 60)
    
    scenarios = [
        {
            "description": "Empty year object {}",
            "expected": "Ignored (not saved)",
            "years": {"2024": {}}
        },
        {
            "description": "Year with all zero quantities",
            "expected": "Saved to database",
            "years": {"2024": {"month_1": {"month": 1, "quantity": 0}}}
        },
        {
            "description": "Year with positive quantities",
            "expected": "Saved to database",
            "years": {"2024": {"month_1": {"month": 1, "quantity": 10}}}
        },
        {
            "description": "Year with mix of zero and positive",
            "expected": "Saved to database",
            "years": {"2024": {
                "month_1": {"month": 1, "quantity": 0},
                "month_2": {"month": 2, "quantity": 10}
            }}
        }
    ]
    
    for scenario in scenarios:
        print(f"📌 {scenario['description']}")
        print(f"   Expected: {scenario['expected']}")
    
    print("\n🎯 Key Behavior Changes:")
    print("- ✅ Empty years {} are ignored")
    print("- ✅ Years with zero quantities are saved")
    print("- ✅ Years with positive quantities are saved")
    print("- ✅ Mixed zero/positive quantities are saved")

def main():
    """Run all tests"""
    print("🚀 Testing Zero Quantities Behavior")
    print("=" * 50)
    
    # Test mixed zero and positive quantities
    success1 = test_zero_quantities_saved()
    
    # Test year with all zero quantities
    success2 = test_all_zero_quantities_year()
    
    # Show behavior comparison
    test_behavior_comparison()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 Zero quantities behavior verification completed successfully!")
        print("\n📝 Confirmed Behavior:")
        print("- ✅ Years with all zero quantities ARE saved to database")
        print("- ✅ Empty year objects {} are NOT saved (ignored)")
        print("- ✅ Mixed zero/positive quantities work correctly")
        print("- ✅ Database stores zero values properly")
    else:
        print("❌ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
