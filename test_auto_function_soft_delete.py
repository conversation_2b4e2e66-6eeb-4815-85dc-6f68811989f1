#!/usr/bin/env python3
"""
Test script to verify auto function API with soft delete functionality
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:5000"
AUTH_ENDPOINT = f"{BASE_URL}/auth/login"
AUTO_FUNCTION_ENDPOINT = f"{BASE_URL}/auto-function"

# Test credentials
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

def authenticate():
    """Get JWT token for authentication"""
    print("🔐 Authenticating...")
    try:
        response = requests.post(AUTH_ENDPOINT, json=TEST_CREDENTIALS)
        if response.status_code == 200:
            data = response.json()
            token = data.get('data', {}).get('access_token')
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            print("✅ Authentication successful")
            return headers
        else:
            print(f"❌ Authentication failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return None

def get_customers_for_test(headers):
    """Get customers for testing"""
    try:
        response = requests.get(f"{AUTO_FUNCTION_ENDPOINT}/customers", headers=headers)
        if response.status_code == 200:
            customers = response.json().get('data', [])
            if customers:
                return [customers[0]['id']]  # Return first customer ID
        return [1]  # Fallback customer ID
    except Exception as e:
        print(f"⚠️ Could not get customers, using fallback: {str(e)}")
        return [1]

def test_create_auto_function():
    """Test creating auto function"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n📝 Testing Auto Function Creation...")
    
    # Get customers for testing
    customer_ids = get_customers_for_test(headers)
    
    auto_function_data = {
        "code": "TEST_SOFT_DELETE_001",
        "name": "Test Auto Function for Soft Delete",
        "note": "This is a test auto function for soft delete functionality",
        "customer_ids": customer_ids
    }
    
    try:
        response = requests.post(AUTO_FUNCTION_ENDPOINT, 
                               json=auto_function_data, 
                               headers=headers)
        
        if response.status_code == 201:
            data = response.json()
            auto_function_id = data.get('data', {}).get('id')
            print(f"✅ Auto function created successfully with ID: {auto_function_id}")
            return auto_function_id
        else:
            print(f"❌ Auto function creation failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Auto function creation error: {str(e)}")
        return None

def test_get_auto_functions():
    """Test getting all auto functions"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n📋 Testing Get All Auto Functions...")
    
    try:
        response = requests.get(AUTO_FUNCTION_ENDPOINT, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            auto_functions = data.get('data', [])
            print(f"✅ Retrieved {len(auto_functions)} auto functions")
            
            # Show sample data
            if auto_functions:
                sample = auto_functions[0]
                print(f"   Sample: {sample.get('name')} (ID: {sample.get('id')})")
                print(f"   Customers: {sample.get('customer_names', [])}")
            
            return True
        else:
            print(f"❌ Get auto functions failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Get auto functions error: {str(e)}")
        return False

def test_get_auto_function_by_id(auto_function_id):
    """Test getting auto function by ID"""
    headers = authenticate()
    if not headers:
        return False
    
    print(f"\n🔍 Testing Get Auto Function by ID: {auto_function_id}...")
    
    try:
        response = requests.get(f"{AUTO_FUNCTION_ENDPOINT}/{auto_function_id}", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            auto_function = data.get('data', {})
            print(f"✅ Retrieved auto function: {auto_function.get('name')}")
            print(f"   Code: {auto_function.get('code')}")
            print(f"   Customers: {auto_function.get('customer_names', [])}")
            return True
        else:
            print(f"❌ Get auto function by ID failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Get auto function by ID error: {str(e)}")
        return False

def test_update_auto_function(auto_function_id):
    """Test updating auto function"""
    headers = authenticate()
    if not headers:
        return False
    
    print(f"\n✏️ Testing Update Auto Function: {auto_function_id}...")
    
    # Get customers for testing
    customer_ids = get_customers_for_test(headers)
    
    update_data = {
        "code": "TEST_SOFT_DELETE_001_UPDATED",
        "name": "Test Auto Function for Soft Delete (Updated)",
        "note": "This is an updated test auto function for soft delete functionality",
        "customer_ids": customer_ids
    }
    
    try:
        response = requests.put(f"{AUTO_FUNCTION_ENDPOINT}/{auto_function_id}", 
                              json=update_data, 
                              headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            updated_auto_function = data.get('data', {})
            print(f"✅ Auto function updated successfully")
            print(f"   New name: {updated_auto_function.get('name')}")
            return True
        else:
            print(f"❌ Auto function update failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Auto function update error: {str(e)}")
        return False

def test_soft_delete_auto_function(auto_function_id):
    """Test soft deleting auto function"""
    headers = authenticate()
    if not headers:
        return False
    
    print(f"\n🗑️ Testing Soft Delete Auto Function: {auto_function_id}...")
    
    try:
        # First verify the auto function exists
        response = requests.get(f"{AUTO_FUNCTION_ENDPOINT}/{auto_function_id}", headers=headers)
        if response.status_code != 200:
            print("❌ Auto function not found before deletion")
            return False
        
        print("✅ Auto function exists before deletion")
        
        # Perform soft delete
        response = requests.delete(f"{AUTO_FUNCTION_ENDPOINT}/{auto_function_id}", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            message = data.get('message', '')
            if "soft delete" in message.lower():
                print(f"✅ Auto function soft deleted successfully")
                print(f"   Message: {message}")
            else:
                print(f"⚠️ Delete successful but message doesn't indicate soft delete: {message}")
            
            # Verify auto function is no longer accessible
            print("🔍 Verifying auto function is no longer accessible...")
            response = requests.get(f"{AUTO_FUNCTION_ENDPOINT}/{auto_function_id}", headers=headers)
            
            if response.status_code == 404:
                print("✅ Auto function is no longer accessible (soft deleted)")
                return True
            else:
                print("❌ Auto function is still accessible after soft delete")
                return False
        else:
            print(f"❌ Auto function soft delete failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Auto function soft delete error: {str(e)}")
        return False

def test_search_functionality():
    """Test search functionality"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n🔍 Testing Search Functionality...")
    
    try:
        # Test search by name
        response = requests.get(f"{AUTO_FUNCTION_ENDPOINT}?search=Nitory", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            auto_functions = data.get('data', [])
            print(f"✅ Search by 'Nitory' returned {len(auto_functions)} results")
            
            if auto_functions:
                for af in auto_functions[:3]:  # Show first 3 results
                    print(f"   - {af.get('name')} ({af.get('code')})")
            
            return True
        else:
            print(f"❌ Search functionality failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Search functionality error: {str(e)}")
        return False

def test_customers_dropdown():
    """Test customers dropdown endpoint"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n👥 Testing Customers Dropdown...")
    
    try:
        response = requests.get(f"{AUTO_FUNCTION_ENDPOINT}/customers", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            customers = data.get('data', [])
            print(f"✅ Retrieved {len(customers)} customers for dropdown")
            
            if customers:
                sample = customers[0]
                print(f"   Sample: {sample.get('name')} (ID: {sample.get('id')})")
            
            return True
        else:
            print(f"❌ Customers dropdown failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Customers dropdown error: {str(e)}")
        return False

def main():
    """Run all auto function tests"""
    print("🚀 Testing Auto Function API with Soft Delete")
    print("=" * 60)
    
    # Test customers dropdown
    success1 = test_customers_dropdown()
    
    # Test get all auto functions
    success2 = test_get_auto_functions()
    
    # Test search functionality
    success3 = test_search_functionality()
    
    # Test create auto function
    auto_function_id = test_create_auto_function()
    success4 = auto_function_id is not None
    
    # Test get by ID
    success5 = test_get_auto_function_by_id(auto_function_id) if auto_function_id else False
    
    # Test update
    success6 = test_update_auto_function(auto_function_id) if auto_function_id else False
    
    # Test soft delete
    success7 = test_soft_delete_auto_function(auto_function_id) if auto_function_id else False
    
    print("\n" + "=" * 60)
    if all([success1, success2, success3, success4, success5, success6, success7]):
        print("🎉 Auto Function API with soft delete verification completed successfully!")
        print("\n📝 Verified Features:")
        print("- ✅ Get all auto functions (with soft delete filter)")
        print("- ✅ Get auto function by ID (with soft delete filter)")
        print("- ✅ Create auto function")
        print("- ✅ Update auto function")
        print("- ✅ Soft delete auto function")
        print("- ✅ Search functionality")
        print("- ✅ Customers dropdown")
        print("- ✅ Data isolation after soft delete")
    else:
        print("❌ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
