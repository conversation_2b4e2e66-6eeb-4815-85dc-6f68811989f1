from ..dao.auto_function_dao import AutoFunctionDAO

class AutoFunctionService:
    def __init__(self):
        self.auto_function_dao = AutoFunctionDAO()
    
    def get_all(self, filters=None):
        return self.auto_function_dao.get_all(filters)
        
    def get_by_id(self, id):
        return self.auto_function_dao.get_by_id(id)
    
    def create(self, data):
        # Validate required fields
        if not data.get('code'):
            raise ValueError("Code is required")
        if not data.get('name'):
            raise ValueError("Name is required")
        if not data.get('customer_ids'):
            raise ValueError("At least one customer must be selected")
            
        # Check if customer_ids is not empty
        if not data['customer_ids']:
            raise ValueError("At least one customer must be selected")
            
        return self.auto_function_dao.create(data)

    def update(self, id, data):
        # Validate required fields
        if not data.get('code'):
            raise ValueError("Code is required")
        if not data.get('name'):
            raise ValueError("Name is required")
        if not data.get('customer_ids'):
            raise ValueError("At least one customer must be selected")
            
        # Check if customer_ids is not empty
        if not data['customer_ids']:
            raise ValueError("At least one customer must be selected")
            
        # Check if auto function exists
        existing = self.get_by_id(id)
        if not existing:
            raise ValueError("Auto function not found")
            
        return self.auto_function_dao.update(id, data)

    def delete(self, id):
        # Check if auto function exists
        existing = self.get_by_id(id)
        if not existing:
            raise ValueError("Auto function not found")
            
        return self.auto_function_dao.delete(id)

    def seed_auto_focus_data(self):
        try:
            return self.auto_function_dao.seed_auto_focus_data()
        except Exception as e:
            print(f"Error in seed_auto_focus_data service: {str(e)}")
            raise e

    def get_customers_for_dropdown(self):
        """Get customers for dropdown"""
        try:
            return self.auto_function_dao.get_customers_for_dropdown()
        except Exception as e:
            print(f"Error in get_customers_for_dropdown: {str(e)}")
            raise e
