#!/usr/bin/env python3
"""
Test script to verify the specific payload with empty years
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:5000"
AUTH_ENDPOINT = f"{BASE_URL}/auth/login"
WORKOUT_CHARGE_ENDPOINT = f"{BASE_URL}/workout-charges"

# Test credentials
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

def authenticate():
    """Get JWT token for authentication"""
    print("🔐 Authenticating...")
    try:
        response = requests.post(AUTH_ENDPOINT, json=TEST_CREDENTIALS)
        if response.status_code == 200:
            data = response.json()
            token = data.get('data', {}).get('access_token')
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            print("✅ Authentication successful")
            return headers
        else:
            print(f"❌ Authentication failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return None

def test_original_failing_payload():
    """Test the exact payload that was failing"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n🧪 Testing Original Failing Payload...")
    
    # The exact payload from the user
    payload = {
        "customer_id": 2,
        "division_id": 3,
        "workout_customer_charge_content": {
            "1749032856261_4uzgezu": {
                "category_id": "13",
                "category_name": "Fitness Updated",
                "workouts": {
                    "1749032859044_xty7dbh": {
                        "group_id": "28",
                        "group_name": "Cardio Workout",
                        "unit_price": "50000",
                        "years": {
                            "2025": {},
                            "2026": {}
                        },
                        "calculation_type": "Manual"
                    }
                }
            }
        }
    }
    
    print("📝 Testing POST /workout-charges/ (CREATE)...")
    try:
        response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/", 
                               json=payload, 
                               headers=headers)
        
        if response.status_code == 201:
            print("✅ CREATE with empty years succeeded!")
            data = response.json()
            print(f"   Response: {data.get('message')}")
            return True
        else:
            print(f"❌ CREATE with empty years failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        return False

def test_replace_with_empty_years():
    """Test replace with empty years"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n🔄 Testing REPLACE with Empty Years...")
    
    # Same payload but for replace
    payload = {
        "customer_id": 2,
        "division_id": 4,  # Different division
        "workout_customer_charge_content": {
            "1749032856261_4uzgezu": {
                "category_id": "13",
                "category_name": "Fitness Updated",
                "workouts": {
                    "1749032859044_xty7dbh": {
                        "group_id": "28",
                        "group_name": "Cardio Workout",
                        "unit_price": "50000",
                        "years": {
                            "2025": {},
                            "2026": {}
                        },
                        "calculation_type": "Manual"
                    }
                }
            }
        }
    }
    
    print("📝 Testing POST /workout-charges/replace...")
    try:
        response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/replace", 
                               json=payload, 
                               headers=headers)
        
        if response.status_code == 200:
            print("✅ REPLACE with empty years succeeded!")
            data = response.json()
            print(f"   Response: {data.get('message')}")
            
            # Verify no data was actually saved
            print("🔍 Verifying no database records created...")
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/customer/2/division/4", 
                                  headers=headers)
            if response.status_code == 200:
                saved_data = response.json().get('data')
                if saved_data is None:
                    print("✅ Correctly no data saved (empty years)")
                else:
                    print(f"⚠️ Unexpected data saved: {saved_data}")
            else:
                print("✅ Correctly no data found (empty years)")
            
            return True
        else:
            print(f"❌ REPLACE with empty years failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        return False

def test_mixed_empty_and_data_years():
    """Test payload with mix of empty years and years with data"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n🔀 Testing Mixed Empty and Data Years...")
    
    # Payload with some empty years and some with data
    payload = {
        "customer_id": 2,
        "division_id": 5,  # Different division
        "workout_customer_charge_content": {
            "category_13": {
                "category_id": "13",
                "category_name": "Fitness Updated",
                "workouts": {
                    "workout_28": {
                        "group_id": "28",
                        "group_name": "Cardio Workout",
                        "unit_price": "50000",
                        "years": {
                            "2024": {},  # Empty year
                            "2025": {
                                "month_1": {"month": 1, "quantity": 10},
                                "month_2": {"month": 2, "quantity": 0}
                            },
                            "2026": {}  # Empty year
                        },
                        "calculation_type": "Manual"
                    }
                }
            }
        }
    }
    
    print("📝 Testing REPLACE with mixed empty/data years...")
    try:
        response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/replace", 
                               json=payload, 
                               headers=headers)
        
        if response.status_code == 200:
            print("✅ REPLACE with mixed years succeeded!")
            
            # Verify only 2025 data was saved
            print("🔍 Verifying only year with data was saved...")
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/customer/2/division/5", 
                                  headers=headers)
            if response.status_code == 200:
                saved_data = response.json().get('data', {})
                content = saved_data.get('workout_customer_charge_content', {})
                
                if content:
                    for category_key, category_data in content.items():
                        for workout_key, workout_data in category_data.get('workouts', {}).items():
                            years = workout_data.get('years', {})
                            print(f"   Saved years: {list(years.keys())}")
                            
                            if '2025' in years and '2024' not in years and '2026' not in years:
                                print("✅ Correctly saved only year 2025 with data")
                            else:
                                print(f"⚠️ Unexpected years saved: {list(years.keys())}")
                else:
                    print("❌ No data found")
            else:
                print(f"❌ Failed to retrieve data: {response.text}")
            
            return True
        else:
            print(f"❌ REPLACE with mixed years failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Empty Years Payload Fix")
    print("=" * 60)
    
    # Test original failing payload
    success1 = test_original_failing_payload()
    
    # Test replace with empty years
    success2 = test_replace_with_empty_years()
    
    # Test mixed empty and data years
    success3 = test_mixed_empty_and_data_years()
    
    print("\n" + "=" * 60)
    if success1 and success2 and success3:
        print("🎉 Empty years payload fix verification completed successfully!")
        print("\n📝 Verified Behavior:")
        print("- ✅ Empty years {} are ignored during processing")
        print("- ✅ No database records created for empty years")
        print("- ✅ Frontend can create structure without month data")
        print("- ✅ Mixed empty/data years work correctly")
        print("- ✅ API returns success even with all empty years")
    else:
        print("❌ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
