# Customer API Documentation

Tài liệu này mô tả các API endpoints cho Customer management được tạo dựa trên các function trong `service_code.ts`.

## Base URL
```
/customers
```

## Authentication
Tất cả các endpoints đều yêu cầu JWT authentication thông qua header:
```
Authorization: Bearer <token>
```

## Customer Endpoints

### 1. Get All Customers
**Endpoint:** `GET /customers/`  
**Mô tả:** L<PERSON>y danh sách tất cả customers  
**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "code": "CUST001",
      "name": "Customer Name",
      "address": "Customer Address",
      "taxNumber": "*********",
      "phone": "0*********",
      "status": "active"
    }
  ]
}
```

### 2. Get Customer by ID
**Endpoint:** `GET /customers/{customer_id}`  
**Mô tả:** <PERSON><PERSON><PERSON> thông tin customer theo ID  
**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "code": "CUST001",
    "name": "Customer Name",
    "address": "Customer Address",
    "taxNumber": "*********",
    "phone": "0*********",
    "status": "active"
  }
}
```

### 3. Create Customer
**Endpoint:** `POST /customers/`  
**Mô tả:** Tạo customer mới  
**Request Body:**
```json
{
  "code": "CUST001",
  "name": "Customer Name",
  "address": "Customer Address",
  "taxNumber": "*********",
  "phone": "0*********"
}
```
**Response:**
```json
{
  "status": "success",
  "message": "Customer created successfully",
  "data": {"id": 1}
}
```

### 4. Update Customer
**Endpoint:** `PUT /customers/{customer_id}`  
**Mô tả:** Cập nhật thông tin customer  
**Request Body:**
```json
{
  "code": "CUST001_UPDATED",
  "name": "Updated Customer Name",
  "address": "Updated Address",
  "taxNumber": "*********",
  "phone": "0*********",
  "status": "inactive"
}
```
**Response:**
```json
{
  "status": "success",
  "message": "Customer updated successfully"
}
```

### 5. Delete Customer
**Endpoint:** `DELETE /customers/{customer_id}`  
**Mô tả:** Xóa customer (chỉ được phép xóa customer có status khác 'active')  
**Response:**
```json
{
  "status": "success",
  "message": "Customer deleted successfully"
}
```

## Customer Settings Endpoints

### 6. Get Customer Settings
**Endpoint:** `GET /customers/{customer_id}/settings`  
**Mô tả:** Lấy settings của customer  
**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "customerId": 1,
    "masterDataImportMapping": {
      "field1": "mapping1",
      "field2": "mapping2"
    },
    "createdAt": "2024-01-01T00:00:00",
    "updatedAt": "2024-01-01T00:00:00"
  }
}
```

### 7. Add/Update Customer Settings
**Endpoint:** `POST /customers/{customer_id}/settings`  
**Mô tả:** Thêm hoặc cập nhật settings cho customer (UPSERT)  
**Request Body:**
```json
{
  "masterDataImportMapping": {
    "field1": "mapping1",
    "field2": "mapping2"
  }
}
```
**Response:**
```json
{
  "status": "success",
  "message": "Customer settings saved successfully"
}
```

## Customer Divisions Endpoints

### 8. Get Customer Divisions
**Endpoint:** `GET /customers/{customer_id}/divisions`  
**Mô tả:** Lấy danh sách divisions của customer  
**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "code": "DIV001",
      "name": "Division Name",
      "description": "Division Description",
      "customerId": 1,
      "status": "active"
    }
  ]
}
```

### 9. Get Division by ID
**Endpoint:** `GET /customers/divisions/{division_id}`  
**Mô tả:** Lấy thông tin division theo ID  
**Response:**
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "code": "DIV001",
    "name": "Division Name",
    "description": "Division Description",
    "customerId": 1,
    "status": "active"
  }
}
```

### 10. Create Customer Division
**Endpoint:** `POST /customers/{customer_id}/divisions`  
**Mô tả:** Tạo division mới cho customer  
**Request Body:**
```json
{
  "code": "DIV001",
  "name": "Division Name",
  "description": "Division Description"
}
```
**Response:**
```json
{
  "status": "success",
  "message": "Customer division created successfully",
  "data": {"id": 1}
}
```

### 11. Update Customer Division
**Endpoint:** `PUT /customers/divisions/{division_id}`  
**Mô tả:** Cập nhật thông tin division  
**Request Body:**
```json
{
  "code": "DIV001_UPDATED",
  "name": "Updated Division Name",
  "description": "Updated Description",
  "status": "inactive"
}
```
**Response:**
```json
{
  "status": "success",
  "message": "Customer division updated successfully"
}
```

### 12. Delete Customer Division
**Endpoint:** `DELETE /customers/divisions/{division_id}`  
**Mô tả:** Xóa division (chỉ được phép xóa division có status khác 'active')  
**Response:**
```json
{
  "status": "success",
  "message": "Customer division deleted successfully"
}
```

## Error Responses

Tất cả các endpoints đều có thể trả về các lỗi sau:

### 400 Bad Request
```json
{
  "status": "error",
  "message": "Missing required field: name"
}
```

### 404 Not Found
```json
{
  "status": "error",
  "message": "Customer not found"
}
```

### 500 Internal Server Error
```json
{
  "status": "error",
  "message": "Failed to create customer"
}
```

## Database Tables

API này sử dụng các bảng sau trong PostgreSQL:

1. **customers** - Lưu thông tin customer
   - id, code, name, address, tax_number, phone, status

2. **customer_settings** - Lưu settings của customer
   - id, customer_id, master_data_import_mapping, created_at, updated_at

3. **customer_divisions** - Lưu thông tin divisions của customer
   - id, code, name, description, customer_id, status

## Notes

- Tất cả các API đều follow pattern của source code hiện tại
- Sử dụng raw SQL queries thông qua DAO layer
- Có validation cho business logic (không được xóa active records)
- Support dynamic updates (chỉ update các field được cung cấp)
- JSON response format nhất quán với các API khác trong hệ thống
