-- Workout Customer Charge Tables
-- Run this script to create the necessary tables for Workout Customer Charge API

-- Create workout_customer_charge table
CREATE TABLE IF NOT EXISTS workout_customer_charge (
    id TEXT PRIMARY KEY,
    customer_id INTEGER NOT NULL,
    division_id INTEGER NULL,
    category_id TEXT NOT NULL,
    workout_id TEXT NOT NULL,
    year INTEGER NOT NULL,
    month_1 INTEGER NULL,
    month_2 INTEGER NULL,
    month_3 INTEGER NULL,
    month_4 INTEGER NULL,
    month_5 INTEGER NULL,
    month_6 INTEGER NULL,
    month_7 INTEGER NULL,
    month_8 INTEGER NULL,
    month_9 INTEGER NULL,
    month_10 INTEGER NULL,
    month_11 INTEGER NULL,
    month_12 INTEGER NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (division_id) REFERENCES customer_divisions(id) ON DELETE CASCADE
);

-- Create categories table if not exists (for dropdown data)
CREATE TABLE IF NOT EXISTS categories (
    id TEXT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create workout table if not exists (for dropdown data)
CREATE TABLE IF NOT EXISTS workout (
    id TEXT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    category_id TEXT NOT NULL,
    unit_price DECIMAL(15,2) DEFAULT 0,
    calculation_type VARCHAR(50) DEFAULT 'Auto',
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_workout_customer_charge_customer_id ON workout_customer_charge(customer_id);
CREATE INDEX IF NOT EXISTS idx_workout_customer_charge_division_id ON workout_customer_charge(division_id);
CREATE INDEX IF NOT EXISTS idx_workout_customer_charge_category_id ON workout_customer_charge(category_id);
CREATE INDEX IF NOT EXISTS idx_workout_customer_charge_workout_id ON workout_customer_charge(workout_id);
CREATE INDEX IF NOT EXISTS idx_workout_customer_charge_year ON workout_customer_charge(year);
CREATE INDEX IF NOT EXISTS idx_workout_customer_charge_customer_division ON workout_customer_charge(customer_id, division_id);

CREATE INDEX IF NOT EXISTS idx_categories_status ON categories(status);
CREATE INDEX IF NOT EXISTS idx_workout_category_id ON workout(category_id);
CREATE INDEX IF NOT EXISTS idx_workout_status ON workout(status);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables
DROP TRIGGER IF EXISTS update_workout_customer_charge_updated_at ON workout_customer_charge;
CREATE TRIGGER update_workout_customer_charge_updated_at 
    BEFORE UPDATE ON workout_customer_charge 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_categories_updated_at ON categories;
CREATE TRIGGER update_categories_updated_at 
    BEFORE UPDATE ON categories 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_workout_updated_at ON workout;
CREATE TRIGGER update_workout_updated_at 
    BEFORE UPDATE ON workout 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample categories
INSERT INTO categories (id, name, description, status) VALUES
('CAT001', 'Fitness Training', 'General fitness and conditioning programs', 'active'),
('CAT002', 'Strength Training', 'Weight lifting and muscle building programs', 'active'),
('CAT003', 'Cardio Workout', 'Cardiovascular and endurance training', 'active'),
('CAT004', 'Yoga & Meditation', 'Mind-body wellness and flexibility programs', 'active'),
('CAT005', 'Sports Training', 'Sport-specific training and conditioning', 'active'),
('CAT006', 'Rehabilitation', 'Physical therapy and injury recovery programs', 'active')
ON CONFLICT (id) DO NOTHING;

-- Insert sample workouts
INSERT INTO workout (id, name, description, category_id, unit_price, calculation_type, status) VALUES
-- Fitness Training workouts
('WORKOUT_FIT_001', 'Basic Fitness Program', 'Beginner-friendly fitness routine', 'CAT001', 50000, 'Auto', 'active'),
('WORKOUT_FIT_002', 'Advanced Fitness Program', 'High-intensity fitness training', 'CAT001', 75000, 'Manual', 'active'),
('WORKOUT_FIT_003', 'Group Fitness Class', 'Team-based fitness activities', 'CAT001', 40000, 'Auto', 'active'),

-- Strength Training workouts
('WORKOUT_STR_001', 'Beginner Weight Training', 'Introduction to weight lifting', 'CAT002', 60000, 'Auto', 'active'),
('WORKOUT_STR_002', 'Powerlifting Program', 'Advanced strength building', 'CAT002', 90000, 'Manual', 'active'),
('WORKOUT_STR_003', 'Bodybuilding Program', 'Muscle mass development', 'CAT002', 80000, 'Manual', 'active'),

-- Cardio Workout
('WORKOUT_CAR_001', 'Running Program', 'Endurance running training', 'CAT003', 45000, 'Auto', 'active'),
('WORKOUT_CAR_002', 'HIIT Training', 'High-intensity interval training', 'CAT003', 55000, 'Auto', 'active'),
('WORKOUT_CAR_003', 'Cycling Program', 'Indoor and outdoor cycling', 'CAT003', 50000, 'Auto', 'active'),

-- Yoga & Meditation
('WORKOUT_YOG_001', 'Hatha Yoga', 'Traditional yoga practice', 'CAT004', 40000, 'Auto', 'active'),
('WORKOUT_YOG_002', 'Vinyasa Flow', 'Dynamic yoga sequences', 'CAT004', 45000, 'Auto', 'active'),
('WORKOUT_YOG_003', 'Meditation Sessions', 'Mindfulness and meditation', 'CAT004', 35000, 'Auto', 'active'),

-- Sports Training
('WORKOUT_SPT_001', 'Football Training', 'Soccer-specific conditioning', 'CAT005', 70000, 'Manual', 'active'),
('WORKOUT_SPT_002', 'Basketball Training', 'Basketball skills and fitness', 'CAT005', 65000, 'Manual', 'active'),
('WORKOUT_SPT_003', 'Tennis Training', 'Tennis technique and conditioning', 'CAT005', 60000, 'Manual', 'active'),

-- Rehabilitation
('WORKOUT_REH_001', 'Physical Therapy', 'Injury recovery and rehabilitation', 'CAT006', 80000, 'Manual', 'active'),
('WORKOUT_REH_002', 'Post-Surgery Recovery', 'Specialized recovery programs', 'CAT006', 100000, 'Manual', 'active'),
('WORKOUT_REH_003', 'Chronic Pain Management', 'Pain relief and mobility improvement', 'CAT006', 85000, 'Manual', 'active')
ON CONFLICT (id) DO NOTHING;

-- Insert sample workout customer charge data
INSERT INTO workout_customer_charge (
    id, customer_id, division_id, category_id, workout_id, year,
    month_1, month_2, month_3, month_4, month_5, month_6,
    month_7, month_8, month_9, month_10, month_11, month_12
) VALUES
-- Sample data for customer 1, division 1, 2024
('WCC_SAMPLE_001', 1, 1, 'CAT001', 'WORKOUT_FIT_001', 2024, 10, 12, 15, 8, 20, 18, 22, 25, 16, 14, 19, 21),
('WCC_SAMPLE_002', 1, 1, 'CAT002', 'WORKOUT_STR_001', 2024, 5, 8, 10, 6, 12, 9, 11, 13, 8, 7, 10, 12),
('WCC_SAMPLE_003', 1, 1, 'CAT003', 'WORKOUT_CAR_001', 2024, 15, 18, 20, 12, 25, 22, 28, 30, 20, 18, 24, 26),

-- Sample data for customer 2, division 3, 2024
('WCC_SAMPLE_004', 2, 3, 'CAT004', 'WORKOUT_YOG_001', 2024, 8, 10, 12, 6, 15, 13, 16, 18, 12, 10, 14, 16),
('WCC_SAMPLE_005', 2, 3, 'CAT005', 'WORKOUT_SPT_001', 2024, 12, 15, 18, 10, 20, 17, 22, 25, 16, 14, 19, 21)
ON CONFLICT (id) DO NOTHING;

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON workout_customer_charge TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON categories TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON workout TO your_app_user;

-- Display table information
SELECT 'workout_customer_charge' as table_name, COUNT(*) as record_count FROM workout_customer_charge
UNION ALL
SELECT 'categories' as table_name, COUNT(*) as record_count FROM categories
UNION ALL
SELECT 'workout' as table_name, COUNT(*) as record_count FROM workout;

-- Add comments
COMMENT ON TABLE workout_customer_charge IS 'Stores workout customer charge data with monthly quantities';
COMMENT ON TABLE categories IS 'Stores workout categories for classification';
COMMENT ON TABLE workout IS 'Stores workout programs and their details';

COMMENT ON COLUMN workout_customer_charge.category_id IS 'Reference to category (TEXT type for flexibility)';
COMMENT ON COLUMN workout_customer_charge.workout_id IS 'Reference to workout program (TEXT type for flexibility)';
COMMENT ON COLUMN workout_customer_charge.year IS 'Year for the workout charge data';
COMMENT ON COLUMN workout_customer_charge.month_1 IS 'Quantity for January';
COMMENT ON COLUMN workout_customer_charge.month_12 IS 'Quantity for December';

COMMENT ON COLUMN workout.unit_price IS 'Price per unit for the workout program';
COMMENT ON COLUMN workout.calculation_type IS 'Auto or Manual calculation type';
