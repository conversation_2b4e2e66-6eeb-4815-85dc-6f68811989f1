-- Migration script to add is_deleted column to auto function tables for soft delete functionality

-- Add is_deleted column to auto_function table
ALTER TABLE auto_function 
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

-- Add is_deleted column to auto_function_customers table
ALTER TABLE auto_function_customers 
ADD COLUMN IF NOT EXISTS is_deleted BOOLEAN NOT NULL DEFAULT FALSE;

-- Create indexes for better performance on is_deleted queries
CREATE INDEX IF NOT EXISTS idx_auto_function_is_deleted ON auto_function(is_deleted);
CREATE INDEX IF NOT EXISTS idx_auto_function_customers_is_deleted ON auto_function_customers(is_deleted);

-- Create composite indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_auto_function_customers_function_is_deleted 
ON auto_function_customers(auto_function_id, is_deleted);

CREATE INDEX IF NOT EXISTS idx_auto_function_customers_customer_is_deleted 
ON auto_function_customers(customer_id, is_deleted);

-- Comments for documentation
COMMENT ON COLUMN auto_function.is_deleted IS 'Soft delete flag - TRUE means record is deleted';
COMMENT ON COLUMN auto_function_customers.is_deleted IS 'Soft delete flag - TRUE means record is deleted';

-- Verify the changes
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('auto_function', 'auto_function_customers')
AND column_name = 'is_deleted'
ORDER BY table_name;
