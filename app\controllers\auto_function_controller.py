from flask import Blueprint, request, jsonify
from ..services.auto_function_service import AutoFunctionService
from ..middleware.auth_middleware import jwt_required_custom

auto_function_bp = Blueprint('auto_function', __name__)
auto_function_service = AutoFunctionService()

@auto_function_bp.route('/', methods=['GET'])
@jwt_required_custom
def get_all():
    # Get filters from query parameters
    filters = {}
    if request.args.get('search'):
        filters['search'] = request.args.get('search')
    if request.args.get('customer_id'):
        filters['customer_id'] = request.args.get('customer_id')

    auto_functions = auto_function_service.get_all(filters)
    return jsonify({
        "status": "success",
        "data": auto_functions
    }), 200

@auto_function_bp.route('/<int:id>', methods=['GET'])
@jwt_required_custom
def get_by_id(id):
    auto_function = auto_function_service.get_by_id(id)
    if auto_function:
        return jsonify({
            "status": "success",
            "data": auto_function
        }), 200
    return jsonify({
        "status": "error",
        "message": "Auto function not found"
    }), 404

@auto_function_bp.route('/', methods=['POST'])
@jwt_required_custom
def create():
    data = request.get_json()
    required_fields = ['code', 'name', 'customer_ids']
    
    # Validate required fields
    for field in required_fields:
        if field not in data:
            return jsonify({
                "status": "error",
                "message": f"Missing required field: {field}"
            }), 400
    
    try:
        new_id = auto_function_service.create(data)
        return jsonify({
            "status": "success",
            "message": "Auto function created successfully",
            "data": {"id": new_id}
        }), 201
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to create auto function"
        }), 500

@auto_function_bp.route('/<int:id>', methods=['PUT'])
@jwt_required_custom
def update(id):
    data = request.get_json()
    try:
        auto_function_service.update(id, data)
        return jsonify({
            "status": "success",
            "message": "Auto function updated successfully"
        }), 200
    except ValueError as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 400
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to update auto function"
        }), 500

@auto_function_bp.route('/<int:id>', methods=['DELETE'])
@jwt_required_custom
def delete(id):
    try:
        if auto_function_service.delete(id):
            return jsonify({
                "status": "success",
                "message": "Auto function deleted successfully (soft delete)"
            }), 200
        return jsonify({
            "status": "error",
            "message": "Auto function not found"
        }), 404
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to delete auto function"
        }), 500

# Dropdown data endpoints
@auto_function_bp.route('/customers', methods=['GET'])
@jwt_required_custom
def get_customers_for_dropdown():
    """Get customers for dropdown"""
    try:
        customers = auto_function_service.get_customers_for_dropdown()

        return jsonify({
            "status": "success",
            "data": customers
        }), 200

    except Exception as e:
        return jsonify({
            "status": "error",
            "message": "Failed to get customers"
        }), 500

@auto_function_bp.route('/seed', methods=['POST'])
# Note: This endpoint intentionally does not require authentication
def seed_auto_focus():
    try:
        if auto_function_service.seed_auto_focus_data():
            return jsonify({
                "status": "success",
                "message": "Auto functions seeded successfully"
            }), 201
        return jsonify({
            "status": "success",
            "message": "Auto functions already exist, skipping seed"
        }), 200
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e)
        }), 500
