from ..dao.workout_charge_dao import WorkoutChargeDAO

class WorkoutChargeService:
    def __init__(self):
        self.workout_charge_dao = WorkoutChargeDAO()
    
    def get_workout_charge_by_id(self, charge_id):
        """Get workout customer charge by ID"""
        return self.workout_charge_dao.get_workout_charge_by_id(charge_id)
    
    def get_workout_charges(self, filters=None):
        """Get all workout customer charges with optional filters"""
        return self.workout_charge_dao.get_workout_charges(filters)
    
    def get_workout_charge_by_customer_division(self, customer_id, division_id):
        """Get workout customer charge by customer and division"""
        return self.workout_charge_dao.get_workout_charge_by_customer_division(customer_id, division_id)
    
    def create_workout_charge(self, data):
        """Create new workout customer charge (append to existing data)"""
        # Validate required fields
        if not data.get('customer_id'):
            raise ValueError("Customer ID is required")

        if not data.get('workout_customer_charge_content'):
            raise ValueError("Workout customer charge content is required")

        content = data.get('workout_customer_charge_content', {})
        if not content or len(content) == 0:
            raise ValueError("Workout customer charge content cannot be empty")

        # Check if workout charge already exists for this customer/division
        existing_charge = self.workout_charge_dao.get_workout_charge_by_customer_division(
            data['customer_id'],
            data.get('division_id')
        )

        if existing_charge:
            raise ValueError(f"Workout charge already exists for this customer/division. Use PUT to update or use replace endpoint.")

        # Validate content structure
        self._validate_workout_charge_content(content)

        return self.workout_charge_dao.create_workout_charge(data)

    def replace_workout_charge(self, data):
        """Replace all workout customer charge data for a customer/division (UPSERT)"""
        # Validate required fields
        if not data.get('customer_id'):
            raise ValueError("Customer ID is required")

        if not data.get('workout_customer_charge_content'):
            raise ValueError("Workout customer charge content is required")

        content = data.get('workout_customer_charge_content', {})
        if not content or len(content) == 0:
            raise ValueError("Workout customer charge content cannot be empty")

        # Validate content structure
        self._validate_workout_charge_content(content)

        return self.workout_charge_dao.replace_workout_charge(data)

    def update_workout_charge(self, charge_id, data):
        """Update workout customer charge (replace all data for customer/division)"""
        # Check if workout charge exists
        existing_charge = self.workout_charge_dao.get_workout_charge_by_id(charge_id)
        if not existing_charge:
            raise ValueError("Workout customer charge not found")

        # Validate required fields
        if not data.get('customer_id'):
            raise ValueError("Customer ID is required")

        if not data.get('workout_customer_charge_content'):
            raise ValueError("Workout customer charge content is required")

        content = data.get('workout_customer_charge_content', {})
        if not content or len(content) == 0:
            raise ValueError("Workout customer charge content cannot be empty")

        # Validate content structure
        self._validate_workout_charge_content(content)

        return self.workout_charge_dao.update_workout_charge(charge_id, data)
    
    def delete_workout_charge(self, charge_id):
        """Delete workout customer charge"""
        # Check if workout charge exists
        existing_charge = self.workout_charge_dao.get_workout_charge_by_id(charge_id)
        if not existing_charge:
            raise ValueError("Workout customer charge not found")
        
        return self.workout_charge_dao.delete_workout_charge(charge_id)
    
    # Dropdown data methods
    def get_customers_for_dropdown(self):
        """Get customers for dropdown"""
        return self.workout_charge_dao.get_customers_for_dropdown()
    
    def get_divisions_by_customer_id(self, customer_id):
        """Get divisions by customer ID for dropdown"""
        if not customer_id:
            raise ValueError("Customer ID is required")
        
        return self.workout_charge_dao.get_divisions_by_customer_id(customer_id)
    
    def get_categories_for_dropdown(self):
        """Get categories for dropdown"""
        return self.workout_charge_dao.get_categories_for_dropdown()

    def get_all_categories(self):
        """Get all categories with detailed information"""
        return self.workout_charge_dao.get_all_categories()
    
    def get_category_workouts_by_category_id(self, category_id):
        """Get category workouts by category ID for dropdown"""
        if not category_id:
            raise ValueError("Category ID is required")

        return self.workout_charge_dao.get_category_workouts_by_category_id(category_id)

    def _validate_workout_charge_content(self, content):
        """Validate workout customer charge content structure"""
        for category_key, category_data in content.items():
            if not category_data.get('category_id'):
                raise ValueError(f"Category ID is required for {category_key}")

            workouts = category_data.get('workouts', {})
            if not workouts:
                raise ValueError(f"At least one workout is required for {category_key}")

            for workout_key, workout_data in workouts.items():
                if not workout_data.get('group_id'):
                    raise ValueError(f"Workout group ID is required for {workout_key}")

                years = workout_data.get('years', {})
                if not years:
                    raise ValueError(f"At least one year is required for {workout_key}")

                # Validate year structure (but allow empty years for frontend flexibility)
                for year, year_data in years.items():
                    # Skip empty years (allow empty year objects)
                    if not year_data or len(year_data) == 0:
                        continue

                    # Check if year has any month data (including zero quantities)
                    has_month_data = False
                    for month_key, month_data in year_data.items():
                        if month_key.startswith('month_') and 'quantity' in month_data:
                            has_month_data = True
                            break

                    # Only raise error if year has data but no valid month structure
                    if not has_month_data and year_data and len(year_data) > 0:
                        raise ValueError(f"Year {year} must have at least one month with quantity data")

                # Allow creating workout charge even without month data (for frontend flexibility)
                # Frontend can create structure first and add data later
