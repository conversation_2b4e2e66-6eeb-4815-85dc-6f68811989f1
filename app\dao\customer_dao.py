from sqlalchemy import create_engine, text
from config import Config
import json

class CustomerDAO:
    def __init__(self):
        self.engine = create_engine(Config.get_db_uri())

    def get_all_customers(self):
        sql = """
            SELECT id, code, name, address, tax_number, phone, status
            FROM customers
            ORDER BY id DESC
        """
        with self.engine.connect() as conn:
            result = conn.execute(text(sql))
            customers = []
            for row in result:
                customers.append({
                    'id': row.id,
                    'code': row.code,
                    'name': row.name,
                    'address': row.address,
                    'taxNumber': row.tax_number,
                    'phone': row.phone,
                    'status': row.status
                })
            return customers

    def get_customer_by_id(self, customer_id):
        sql = """
            SELECT id, code, name, address, tax_number, phone, status
            FROM customers
            WHERE id = :id
        """
        with self.engine.connect() as conn:
            result = conn.execute(text(sql), {'id': customer_id})
            row = result.fetchone()
            if row:
                return {
                    'id': row.id,
                    'code': row.code,
                    'name': row.name,
                    'address': row.address,
                    'taxNumber': row.tax_number,
                    'phone': row.phone,
                    'status': row.status
                }
            return None

    def add_customer(self, customer_data):
        sql = """
            INSERT INTO customers (code, name, address, tax_number, phone)
            VALUES (:code, :name, :address, :tax_number, :phone)
            RETURNING id
        """
        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    result = conn.execute(text(sql), {
                        'code': customer_data.get('code'),
                        'name': customer_data.get('name'),
                        'address': customer_data.get('address'),
                        'tax_number': customer_data.get('taxNumber'),
                        'phone': customer_data.get('phone')
                    })
                    new_id = result.fetchone()[0]
                    return new_id
        except Exception as e:
            print(f"Error in add_customer: {str(e)}")
            raise e

    def update_customer(self, customer_data):
        # Build dynamic SET clauses
        set_clauses = []
        params = {'id': customer_data['id']}

        if 'code' in customer_data:
            set_clauses.append('code = :code')
            params['code'] = customer_data['code']
        if 'name' in customer_data:
            set_clauses.append('name = :name')
            params['name'] = customer_data['name']
        if 'address' in customer_data:
            set_clauses.append('address = :address')
            params['address'] = customer_data['address']
        if 'taxNumber' in customer_data:
            set_clauses.append('tax_number = :tax_number')
            params['tax_number'] = customer_data['taxNumber']
        if 'phone' in customer_data:
            set_clauses.append('phone = :phone')
            params['phone'] = customer_data['phone']
        if 'status' in customer_data:
            set_clauses.append('status = :status')
            params['status'] = customer_data['status']

        if not set_clauses:
            return True  # No fields to update

        sql = f"""
            UPDATE customers
            SET {', '.join(set_clauses)}
            WHERE id = :id
        """

        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    result = conn.execute(text(sql), params)
                    return result.rowcount > 0
        except Exception as e:
            print(f"Error in update_customer: {str(e)}")
            raise e

    def delete_customer(self, customer_id):
        sql = 'DELETE FROM customers WHERE id = :id'
        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    result = conn.execute(text(sql), {'id': customer_id})
                    return result.rowcount > 0
        except Exception as e:
            print(f"Error in delete_customer: {str(e)}")
            raise e

    # Customer Settings methods
    def get_customer_settings(self, customer_id):
        sql = """
            SELECT id, customer_id, master_data_import_mapping, created_at, updated_at
            FROM customer_settings
            WHERE customer_id = :customer_id
        """
        with self.engine.connect() as conn:
            result = conn.execute(text(sql), {'customer_id': customer_id})
            row = result.fetchone()
            if row:
                # Handle both string and dict types for master_data_import_mapping
                mapping = row.master_data_import_mapping
                if mapping is None:
                    mapping = {}
                elif isinstance(mapping, str):
                    mapping = json.loads(mapping)
                elif isinstance(mapping, dict):
                    mapping = mapping
                else:
                    mapping = {}

                return {
                    'id': row.id,
                    'customerId': row.customer_id,
                    'masterDataImportMapping': mapping,
                    'createdAt': str(row.created_at),
                    'updatedAt': str(row.updated_at)
                }
            return None

    def add_or_update_customer_settings(self, settings_data):
        mapping_json = json.dumps(settings_data.get('masterDataImportMapping', {}))
        sql = """
            INSERT INTO customer_settings (customer_id, master_data_import_mapping, updated_at)
            VALUES (:customer_id, :mapping, CURRENT_TIMESTAMP)
            ON CONFLICT(customer_id) DO UPDATE SET
                master_data_import_mapping = EXCLUDED.master_data_import_mapping,
                updated_at = CURRENT_TIMESTAMP
        """
        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    conn.execute(text(sql), {
                        'customer_id': settings_data['customerId'],
                        'mapping': mapping_json
                    })
                    return True
        except Exception as e:
            print(f"Error in add_or_update_customer_settings: {str(e)}")
            raise e

    # Customer Divisions methods
    def get_customer_divisions_by_customer_id(self, customer_id):
        sql = """
            SELECT id, code, name, description, customer_id, status
            FROM customer_divisions
            WHERE customer_id = :customer_id
        """
        with self.engine.connect() as conn:
            result = conn.execute(text(sql), {'customer_id': customer_id})
            divisions = []
            for row in result:
                divisions.append({
                    'id': row.id,
                    'code': row.code,
                    'name': row.name,
                    'description': row.description,
                    'customerId': row.customer_id,
                    'status': row.status
                })
            return divisions

    def get_customer_division_by_id(self, division_id):
        sql = """
            SELECT id, code, name, description, customer_id, status
            FROM customer_divisions
            WHERE id = :id
        """
        with self.engine.connect() as conn:
            result = conn.execute(text(sql), {'id': division_id})
            row = result.fetchone()
            if row:
                return {
                    'id': row.id,
                    'code': row.code,
                    'name': row.name,
                    'description': row.description,
                    'customerId': row.customer_id,
                    'status': row.status
                }
            return None

    def add_customer_division(self, division_data):
        sql = """
            INSERT INTO customer_divisions (code, name, description, customer_id)
            VALUES (:code, :name, :description, :customer_id)
            RETURNING id
        """
        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    result = conn.execute(text(sql), {
                        'code': division_data.get('code'),
                        'name': division_data.get('name'),
                        'description': division_data.get('description'),
                        'customer_id': division_data.get('customerId')
                    })
                    new_id = result.fetchone()[0]
                    return new_id
        except Exception as e:
            print(f"Error in add_customer_division: {str(e)}")
            raise e

    def update_customer_division(self, division_data):
        # Build dynamic SET clauses
        set_clauses = []
        params = {'id': division_data['id']}

        if 'code' in division_data:
            set_clauses.append('code = :code')
            params['code'] = division_data['code']
        if 'name' in division_data:
            set_clauses.append('name = :name')
            params['name'] = division_data['name']
        if 'description' in division_data:
            set_clauses.append('description = :description')
            params['description'] = division_data['description']
        if 'customerId' in division_data:
            set_clauses.append('customer_id = :customer_id')
            params['customer_id'] = division_data['customerId']
        if 'status' in division_data:
            set_clauses.append('status = :status')
            params['status'] = division_data['status']

        if not set_clauses:
            return True  # No fields to update

        sql = f"""
            UPDATE customer_divisions
            SET {', '.join(set_clauses)}
            WHERE id = :id
        """

        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    result = conn.execute(text(sql), params)
                    return result.rowcount > 0
        except Exception as e:
            print(f"Error in update_customer_division: {str(e)}")
            raise e

    def delete_customer_division(self, division_id):
        sql = 'DELETE FROM customer_divisions WHERE id = :id'
        try:
            with self.engine.connect() as conn:
                with conn.begin():
                    result = conn.execute(text(sql), {'id': division_id})
                    return result.rowcount > 0
        except Exception as e:
            print(f"Error in delete_customer_division: {str(e)}")
            raise e
