-- SQL script to insert seed data for auto_function tables
-- Use this if tables already exist and you just want to add the 17 Nitori auto functions

-- Insert seed data (17 Nitori auto functions)
DO $$
DECLARE
    nitori_customer_id INTEGER;
    auto_func_id INTEGER;
    existing_count INTEGER;
BEGIN
    -- Check if data already exists
    SELECT COUNT(*) INTO existing_count FROM auto_function WHERE is_deleted = FALSE;
    
    IF existing_count > 0 THEN
        RAISE NOTICE 'Auto function data already exists (% records found). Skipping seed data insertion.', existing_count;
        RETURN;
    END IF;
    
    -- Try to find Nitori customer
    SELECT id INTO nitori_customer_id 
    FROM customers 
    WHERE (code = 'CUS002' OR name = 'Nitori') AND is_deleted = FALSE
    LIMIT 1;
    
    IF nitori_customer_id IS NOT NULL THEN
        RAISE NOTICE 'Found Nitori customer with ID: %', nitori_customer_id;
        
        -- Insert auto functions and link to Nitori customer
        
        -- 1. Nitory Inbound Auto Calculation Total Cartons Received
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_IN_SUM_CARTON', 'Nitory Inbound Auto Calculation Total Cartons Received', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 2. Nitory Inbound Auto Calculation Total Pieces Received
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_IN_SUM_PCS', 'Nitory Inbound Auto Calculation Total Pieces Received', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 3. Nitory Inbound Auto Calculation Total AI Pieces
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_IN_SUM_AI', 'Nitory Inbound Auto  Calculation Total AI Pieces', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 4. Nitory Inbound Auto Calculation Total m3
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_IN_SUM_M3', 'Nitory Inbound Auto Calculation  Total m3', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 5. Nitory Outbound Auto Calculation Total Cartons Received
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_ALL_SUM_CARTON', 'Nitory Outbound Auto  Calculation Total Cartons Received', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 6. Nitory Outbound Auto Calculation Total Pieces Received
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_ALL_SUM_PCS', 'Nitory Outbound Auto  Calculation Total Pieces Received', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 7. Nitory Outbound Auto Calculation Total m3
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_ALL_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 8. Nitory Outbound Auto Calculation Total Cartons Received for Store
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_STORE_SUM_CARTON', 'Nitory Outbound Auto Calculation Total Cartons Received for Store', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 9. Nitory Outbound Auto Calculation Total Pieces Received for Store
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_STORE_SUM_PCS', 'Nitory Outbound Auto Calculation Total Pieces Received for Store', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 10. Nitory Outbound Auto Calculation Total m3 for Store
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_STORE_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3 for Store', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 11. Nitory Outbound Auto Calculation Total Cartons Received for Customer
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_CUSTOMER_SUM_CARTON', 'Nitory Outbound Auto Calculation Total Cartons Received for Customer', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 12. Nitory Outbound Auto Calculation Total Pieces Received for Customer
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_CUSTOMER_SUM_PCS', 'Nitory Outbound Auto Calculation Total Pieces Received for Customer', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 13. Nitory Outbound Auto Calculation Total m3 for Customer
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_CUSTOMER_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3 for Customer', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 14. Nitory Outbound Auto Calculation Total Labeling
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_CUSTOMER_SUM_LABEL', 'Nitory Outbound Auto Calculation Total Labeling', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 15. Nitory Outbound Auto Calculation Total Cartons without COD
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_NONE_SUM_CARTON', 'Nitory Outbound Auto  Calculation Total Cartons without COD', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 16. Nitory Outbound Auto Calculation Total Pieces without COD
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_NONE_SUM_PCS', 'Nitory Outbound Auto  Calculation Total Pieces without COD', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        -- 17. Nitory Outbound Auto Calculation Total m3 without COD
        INSERT INTO auto_function (code, name, note) 
        VALUES ('N_A_OUT_NONE_SUM_M3', 'Nitory Outbound Auto Calculation Total m3 without COD', 'For Nitory Only')
        RETURNING id INTO auto_func_id;
        INSERT INTO auto_function_customers (auto_function_id, customer_id) 
        VALUES (auto_func_id, nitori_customer_id);
        
        RAISE NOTICE 'Successfully created 17 auto functions and linked them to Nitori customer (ID: %)', nitori_customer_id;
    ELSE
        RAISE NOTICE 'Nitori customer not found. Creating auto functions without customer links.';
        RAISE NOTICE 'You can manually link auto functions to customers later via API.';
        
        -- Insert auto functions without customer links
        INSERT INTO auto_function (code, name, note) VALUES
        ('N_A_IN_SUM_CARTON', 'Nitory Inbound Auto Calculation Total Cartons Received', 'For Nitory Only'),
        ('N_A_IN_SUM_PCS', 'Nitory Inbound Auto Calculation Total Pieces Received', 'For Nitory Only'),
        ('N_A_IN_SUM_AI', 'Nitory Inbound Auto  Calculation Total AI Pieces', 'For Nitory Only'),
        ('N_A_IN_SUM_M3', 'Nitory Inbound Auto Calculation  Total m3', 'For Nitory Only'),
        ('N_A_OUT_ALL_SUM_CARTON', 'Nitory Outbound Auto  Calculation Total Cartons Received', 'For Nitory Only'),
        ('N_A_OUT_ALL_SUM_PCS', 'Nitory Outbound Auto  Calculation Total Pieces Received', 'For Nitory Only'),
        ('N_A_OUT_ALL_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3', 'For Nitory Only'),
        ('N_A_OUT_STORE_SUM_CARTON', 'Nitory Outbound Auto Calculation Total Cartons Received for Store', 'For Nitory Only'),
        ('N_A_OUT_STORE_SUM_PCS', 'Nitory Outbound Auto Calculation Total Pieces Received for Store', 'For Nitory Only'),
        ('N_A_OUT_STORE_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3 for Store', 'For Nitory Only'),
        ('N_A_OUT_CUSTOMER_SUM_CARTON', 'Nitory Outbound Auto Calculation Total Cartons Received for Customer', 'For Nitory Only'),
        ('N_A_OUT_CUSTOMER_SUM_PCS', 'Nitory Outbound Auto Calculation Total Pieces Received for Customer', 'For Nitory Only'),
        ('N_A_OUT_CUSTOMER_SUM_M3', 'Nitory Outbound Auto Calculation  Total m3 for Customer', 'For Nitory Only'),
        ('N_A_OUT_CUSTOMER_SUM_LABEL', 'Nitory Outbound Auto Calculation Total Labeling', 'For Nitory Only'),
        ('N_A_OUT_NONE_SUM_CARTON', 'Nitory Outbound Auto  Calculation Total Cartons without COD', 'For Nitory Only'),
        ('N_A_OUT_NONE_SUM_PCS', 'Nitory Outbound Auto  Calculation Total Pieces without COD', 'For Nitory Only'),
        ('N_A_OUT_NONE_SUM_M3', 'Nitory Outbound Auto Calculation Total m3 without COD', 'For Nitory Only');
        
        RAISE NOTICE 'Successfully created 17 auto functions without customer links.';
    END IF;
END $$;

-- Show results
SELECT 
    'auto_function' as table_name,
    COUNT(*) as record_count
FROM auto_function WHERE is_deleted = FALSE
UNION ALL
SELECT 
    'auto_function_customers' as table_name,
    COUNT(*) as record_count
FROM auto_function_customers WHERE is_deleted = FALSE;

-- Show sample data
SELECT 
    af.id,
    af.code,
    af.name,
    CASE 
        WHEN COUNT(afc.id) > 0 THEN 'Linked to customers'
        ELSE 'No customer links'
    END as customer_status
FROM auto_function af
LEFT JOIN auto_function_customers afc ON af.id = afc.auto_function_id AND afc.is_deleted = FALSE
WHERE af.is_deleted = FALSE
GROUP BY af.id, af.code, af.name
ORDER BY af.id
LIMIT 10;
