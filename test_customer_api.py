#!/usr/bin/env python3
"""
Test script for Customer API endpoints
Run this script to test all Customer API functionality
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:5000"
AUTH_ENDPOINT = f"{BASE_URL}/auth/login"
CUSTOMER_ENDPOINT = f"{BASE_URL}/customers"

# Test credentials (adjust as needed)
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

class CustomerAPITester:
    def __init__(self):
        self.token = None
        self.headers = {"Content-Type": "application/json"}
        
    def authenticate(self):
        """Get JWT token for authentication"""
        print("🔐 Authenticating...")
        try:
            response = requests.post(AUTH_ENDPOINT, json=TEST_CREDENTIALS)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('data', {}).get('access_token')
                self.headers["Authorization"] = f"Bearer {self.token}"
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Authentication error: {str(e)}")
            return False
    
    def test_create_customer(self):
        """Test creating a new customer"""
        print("\n📝 Testing Create Customer...")
        customer_data = {
            "code": "TEST001",
            "name": "Test Customer",
            "address": "123 Test Street",
            "taxNumber": "*********",
            "phone": "0*********"
        }
        
        try:
            response = requests.post(CUSTOMER_ENDPOINT + "/", 
                                   json=customer_data, 
                                   headers=self.headers)
            if response.status_code == 201:
                data = response.json()
                customer_id = data.get('data', {}).get('id')
                print(f"✅ Customer created successfully with ID: {customer_id}")
                return customer_id
            else:
                print(f"❌ Create customer failed: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Create customer error: {str(e)}")
            return None
    
    def test_get_all_customers(self):
        """Test getting all customers"""
        print("\n📋 Testing Get All Customers...")
        try:
            response = requests.get(CUSTOMER_ENDPOINT + "/", headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                customers = data.get('data', [])
                print(f"✅ Retrieved {len(customers)} customers")
                return customers
            else:
                print(f"❌ Get all customers failed: {response.text}")
                return []
        except Exception as e:
            print(f"❌ Get all customers error: {str(e)}")
            return []
    
    def test_get_customer_by_id(self, customer_id):
        """Test getting customer by ID"""
        print(f"\n🔍 Testing Get Customer by ID: {customer_id}...")
        try:
            response = requests.get(f"{CUSTOMER_ENDPOINT}/{customer_id}", 
                                  headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                customer = data.get('data', {})
                print(f"✅ Retrieved customer: {customer.get('name')}")
                return customer
            else:
                print(f"❌ Get customer by ID failed: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Get customer by ID error: {str(e)}")
            return None
    
    def test_update_customer(self, customer_id):
        """Test updating customer"""
        print(f"\n✏️ Testing Update Customer ID: {customer_id}...")
        update_data = {
            "name": "Updated Test Customer",
            "address": "456 Updated Street",
            "phone": "0987654321"
        }
        
        try:
            response = requests.put(f"{CUSTOMER_ENDPOINT}/{customer_id}", 
                                  json=update_data, 
                                  headers=self.headers)
            if response.status_code == 200:
                print("✅ Customer updated successfully")
                return True
            else:
                print(f"❌ Update customer failed: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Update customer error: {str(e)}")
            return False
    
    def test_customer_settings(self, customer_id):
        """Test customer settings endpoints"""
        print(f"\n⚙️ Testing Customer Settings for ID: {customer_id}...")
        
        # Test create/update settings
        settings_data = {
            "masterDataImportMapping": {
                "field1": "mapping1",
                "field2": "mapping2",
                "testField": "testMapping"
            }
        }
        
        try:
            response = requests.post(f"{CUSTOMER_ENDPOINT}/{customer_id}/settings", 
                                   json=settings_data, 
                                   headers=self.headers)
            if response.status_code == 200:
                print("✅ Customer settings saved successfully")
                
                # Test get settings
                response = requests.get(f"{CUSTOMER_ENDPOINT}/{customer_id}/settings", 
                                      headers=self.headers)
                if response.status_code == 200:
                    data = response.json()
                    settings = data.get('data', {})
                    print(f"✅ Retrieved customer settings: {len(settings.get('masterDataImportMapping', {}))} mappings")
                    return True
                else:
                    print(f"❌ Get customer settings failed: {response.text}")
                    return False
            else:
                print(f"❌ Save customer settings failed: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Customer settings error: {str(e)}")
            return False
    
    def test_customer_divisions(self, customer_id):
        """Test customer divisions endpoints"""
        print(f"\n🏢 Testing Customer Divisions for ID: {customer_id}...")
        
        # Test create division
        division_data = {
            "code": "DIV001",
            "name": "Test Division",
            "description": "Test Division Description"
        }
        
        try:
            response = requests.post(f"{CUSTOMER_ENDPOINT}/{customer_id}/divisions", 
                                   json=division_data, 
                                   headers=self.headers)
            if response.status_code == 201:
                data = response.json()
                division_id = data.get('data', {}).get('id')
                print(f"✅ Division created successfully with ID: {division_id}")
                
                # Test get divisions
                response = requests.get(f"{CUSTOMER_ENDPOINT}/{customer_id}/divisions", 
                                      headers=self.headers)
                if response.status_code == 200:
                    data = response.json()
                    divisions = data.get('data', [])
                    print(f"✅ Retrieved {len(divisions)} divisions")
                    
                    # Test update division
                    if division_id:
                        update_data = {"name": "Updated Test Division"}
                        response = requests.put(f"{CUSTOMER_ENDPOINT}/divisions/{division_id}", 
                                              json=update_data, 
                                              headers=self.headers)
                        if response.status_code == 200:
                            print("✅ Division updated successfully")
                        else:
                            print(f"❌ Update division failed: {response.text}")
                    
                    return division_id
                else:
                    print(f"❌ Get divisions failed: {response.text}")
                    return None
            else:
                print(f"❌ Create division failed: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Customer divisions error: {str(e)}")
            return None
    
    def test_delete_division(self, division_id):
        """Test deleting division"""
        if not division_id:
            return
            
        print(f"\n🗑️ Testing Delete Division ID: {division_id}...")
        try:
            response = requests.delete(f"{CUSTOMER_ENDPOINT}/divisions/{division_id}", 
                                     headers=self.headers)
            if response.status_code == 200:
                print("✅ Division deleted successfully")
            else:
                print(f"❌ Delete division failed: {response.text}")
        except Exception as e:
            print(f"❌ Delete division error: {str(e)}")
    
    def test_delete_customer(self, customer_id):
        """Test deleting customer"""
        if not customer_id:
            return
            
        print(f"\n🗑️ Testing Delete Customer ID: {customer_id}...")
        try:
            response = requests.delete(f"{CUSTOMER_ENDPOINT}/{customer_id}", 
                                     headers=self.headers)
            if response.status_code == 200:
                print("✅ Customer deleted successfully")
            else:
                print(f"❌ Delete customer failed: {response.text}")
        except Exception as e:
            print(f"❌ Delete customer error: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Customer API Tests...")
        
        # Authenticate first
        if not self.authenticate():
            print("❌ Cannot proceed without authentication")
            return
        
        # Test customer CRUD
        customer_id = self.test_create_customer()
        self.test_get_all_customers()
        
        if customer_id:
            self.test_get_customer_by_id(customer_id)
            self.test_update_customer(customer_id)
            self.test_customer_settings(customer_id)
            division_id = self.test_customer_divisions(customer_id)
            
            # Cleanup
            self.test_delete_division(division_id)
            self.test_delete_customer(customer_id)
        
        print("\n🎉 All tests completed!")

if __name__ == "__main__":
    tester = CustomerAPITester()
    tester.run_all_tests()
