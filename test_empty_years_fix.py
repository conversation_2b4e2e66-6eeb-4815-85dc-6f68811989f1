#!/usr/bin/env python3
"""
Test script to verify the fix for empty years validation
This tests the specific payload that was failing before
"""

import requests
import json

# Configuration
BASE_URL = "http://localhost:5000"
AUTH_ENDPOINT = f"{BASE_URL}/auth/login"
WORKOUT_CHARGE_ENDPOINT = f"{BASE_URL}/workout-charges"

# Test credentials
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

def authenticate():
    """Get JWT token for authentication"""
    print("🔐 Authenticating...")
    try:
        response = requests.post(AUTH_ENDPOINT, json=TEST_CREDENTIALS)
        if response.status_code == 200:
            data = response.json()
            token = data.get('data', {}).get('access_token')
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token}"
            }
            print("✅ Authentication successful")
            return headers
        else:
            print(f"❌ Authentication failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {str(e)}")
        return None

def test_empty_years_payload():
    """Test the specific payload that was failing"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n🧪 Testing Empty Years Payload Fix...")
    
    # The exact payload that was failing
    payload = {
        "customer_id": 1,
        "division_id": 2,
        "workout_customer_charge_content": {
            "category_CAT001": {
                "category_id": "CAT001",
                "category_name": "Fitness Training",
                "workouts": {
                    "workout_WORKOUT_FIT_002": {
                        "group_id": "WORKOUT_FIT_002",
                        "group_name": "Advanced Fitness Program",
                        "unit_price": "75000.00",
                        "years": {
                            "2025": {
                                "month_1": {"month": 1, "quantity": 200},
                                "month_2": {"month": 2, "quantity": 200},
                                "month_3": {"month": 3, "quantity": 200},
                                "month_4": {"month": 4, "quantity": 200},
                                "month_5": {"month": 5, "quantity": 200},
                                "month_6": {"month": 6, "quantity": 200},
                                "month_7": {"month": 7, "quantity": 200},
                                "month_8": {"month": 8, "quantity": 200},
                                "month_9": {"month": 9, "quantity": 200},
                                "month_10": {"month": 10, "quantity": 200},
                                "month_11": {"month": 11, "quantity": 200},
                                "month_12": {"month": 12, "quantity": 200}
                            },
                            "2026": {},  # Empty year - should be ignored
                            "2027": {}   # Empty year - should be ignored
                        }
                    }
                }
            }
        }
    }
    
    # Test with replace endpoint first (safer)
    print("📝 Testing with REPLACE endpoint...")
    try:
        response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/replace", 
                               json=payload, 
                               headers=headers)
        
        if response.status_code == 200:
            print("✅ REPLACE with empty years succeeded!")
            data = response.json()
            print(f"   Response: {data.get('message')}")
            
            # Verify the data was saved correctly
            print("🔍 Verifying saved data...")
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/customer/1/division/2", 
                                  headers=headers)
            if response.status_code == 200:
                saved_data = response.json().get('data', {})
                content = saved_data.get('workout_customer_charge_content', {})
                
                if 'category_CAT001' in content:
                    workouts = content['category_CAT001'].get('workouts', {})
                    if 'workout_WORKOUT_FIT_002' in workouts:
                        years = workouts['workout_WORKOUT_FIT_002'].get('years', {})
                        print(f"   Saved years: {list(years.keys())}")
                        
                        # Check that empty years were not saved
                        if '2026' not in years and '2027' not in years:
                            print("✅ Empty years correctly excluded from saved data")
                        else:
                            print("⚠️ Empty years were saved (unexpected)")
                        
                        # Check that 2025 data was saved
                        if '2025' in years and years['2025']:
                            print("✅ Valid year data (2025) correctly saved")
                        else:
                            print("❌ Valid year data (2025) missing")
                    else:
                        print("❌ Workout not found in saved data")
                else:
                    print("❌ Category not found in saved data")
            else:
                print(f"❌ Failed to retrieve saved data: {response.text}")
            
            return True
        else:
            print(f"❌ REPLACE with empty years failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test error: {str(e)}")
        return False

def test_various_empty_year_scenarios():
    """Test various scenarios with empty years"""
    headers = authenticate()
    if not headers:
        return False
    
    print("\n🧪 Testing Various Empty Year Scenarios...")
    
    scenarios = [
        {
            "name": "All years empty",
            "years": {
                "2024": {},
                "2025": {},
                "2026": {}
            },
            "should_fail": True
        },
        {
            "name": "Mix of empty and valid years",
            "years": {
                "2024": {},
                "2025": {"month_1": {"month": 1, "quantity": 10}},
                "2026": {}
            },
            "should_fail": False
        },
        {
            "name": "Year with zero quantities",
            "years": {
                "2025": {
                    "month_1": {"month": 1, "quantity": 0},
                    "month_2": {"month": 2, "quantity": 0}
                }
            },
            "should_fail": True
        },
        {
            "name": "Single valid year",
            "years": {
                "2025": {"month_1": {"month": 1, "quantity": 5}}
            },
            "should_fail": False
        }
    ]
    
    for i, scenario in enumerate(scenarios):
        print(f"\n📋 Scenario {i+1}: {scenario['name']}")
        
        payload = {
            "customer_id": 1,
            "division_id": 3,  # Use different division for each test
            "workout_customer_charge_content": {
                "category_CAT001": {
                    "category_id": "CAT001",
                    "workouts": {
                        "workout_WORKOUT_FIT_001": {
                            "group_id": "WORKOUT_FIT_001",
                            "years": scenario["years"]
                        }
                    }
                }
            }
        }
        
        try:
            response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/replace", 
                                   json=payload, 
                                   headers=headers)
            
            if scenario["should_fail"]:
                if response.status_code == 400:
                    print(f"✅ Correctly failed with validation error")
                else:
                    print(f"⚠️ Expected failure but got status {response.status_code}")
            else:
                if response.status_code == 200:
                    print(f"✅ Correctly succeeded")
                else:
                    print(f"❌ Expected success but got status {response.status_code}: {response.text}")
                    
        except Exception as e:
            print(f"❌ Scenario test error: {str(e)}")

def main():
    """Run all tests"""
    print("🚀 Testing Empty Years Fix")
    print("=" * 50)
    
    # Test the original failing payload
    success1 = test_empty_years_payload()
    
    # Test various scenarios
    test_various_empty_year_scenarios()
    
    print("\n" + "=" * 50)
    if success1:
        print("🎉 Empty years fix verification completed successfully!")
        print("\n📝 Summary:")
        print("- ✅ Empty year objects ({}) are now ignored")
        print("- ✅ Only years with valid month data are processed")
        print("- ✅ Validation correctly handles mixed scenarios")
        print("- ✅ Original failing payload now works")
    else:
        print("❌ Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()
