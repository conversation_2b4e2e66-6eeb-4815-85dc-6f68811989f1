from flask import Blueprint, request, jsonify
from ..services.auth_service import AuthService
from flask_jwt_extended import create_access_token
from datetime import timedelta

auth_bp = Blueprint('auth', __name__)
auth_service = AuthService()

@auth_bp.route('/login', methods=['POST'])
def login():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')
    
    if not username or not password:
        return jsonify({"error": "Missing username or password"}), 400
        
    user = auth_service.authenticate(username, password)
    if user:
        access_token = create_access_token(
            identity=str(user['id']),  # Convert user ID to string
            additional_claims={'username': user['username']},
            expires_delta=timedelta(days=1)
        )
        return jsonify({
            "status": "success",
            "data": {
                "token_type": "Bearer",
                "access_token": access_token,
                "expires_in": 86400,  # 24 hours in seconds
                "user": {
                    "id": user['id'],
                    "username": user['username'],
                    "role": user['role'],
                }
            }
        }), 200
    return jsonify({"status": "error", "message": "Invalid credentials"}), 401
