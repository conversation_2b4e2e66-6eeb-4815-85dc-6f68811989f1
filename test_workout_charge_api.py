#!/usr/bin/env python3
"""
Test script for Workout Customer Charge API endpoints
Run this script to test all Workout Customer Charge API functionality
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:5000"
AUTH_ENDPOINT = f"{BASE_URL}/auth/login"
WORKOUT_CHARGE_ENDPOINT = f"{BASE_URL}/workout-charges"

# Test credentials (adjust as needed)
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

class WorkoutChargeAPITester:
    def __init__(self):
        self.token = None
        self.headers = {"Content-Type": "application/json"}
        
    def authenticate(self):
        """Get JWT token for authentication"""
        print("🔐 Authenticating...")
        try:
            response = requests.post(AUTH_ENDPOINT, json=TEST_CREDENTIALS)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('data', {}).get('access_token')
                self.headers["Authorization"] = f"Bearer {self.token}"
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Authentication error: {str(e)}")
            return False
    
    def test_get_dropdown_data(self):
        """Test getting dropdown data"""
        print("\n📋 Testing Dropdown Data...")
        
        # Test get customers
        try:
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/customers", headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                customers = data.get('data', [])
                print(f"✅ Retrieved {len(customers)} customers for dropdown")
                return customers
            else:
                print(f"❌ Get customers failed: {response.text}")
                return []
        except Exception as e:
            print(f"❌ Get customers error: {str(e)}")
            return []

    def test_get_all_categories(self):
        """Test getting all categories with detailed information"""
        print("\n🏷️ Testing Get All Categories...")

        try:
            # Test get categories for dropdown (simple format)
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/categories", headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                categories = data.get('data', [])
                print(f"✅ Retrieved {len(categories)} categories (dropdown format)")
            else:
                print(f"❌ Get categories (dropdown) failed: {response.text}")

            # Test get all categories (detailed format)
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/categories/", headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                all_categories = data.get('data', [])
                total = data.get('total', 0)
                print(f"✅ Retrieved {len(all_categories)} categories (detailed format), total: {total}")

                # Show sample detailed category
                if all_categories:
                    sample_cat = all_categories[0]
                    print(f"   Sample category: {sample_cat.get('name')} - {sample_cat.get('description', 'No description')}")
                    print(f"   Status: {sample_cat.get('status', 'N/A')}")

                    # Test get workouts for first category
                    category_id = sample_cat['id']
                    response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/categories/{category_id}/workouts",
                                          headers=self.headers)
                    if response.status_code == 200:
                        data = response.json()
                        workouts = data.get('data', [])
                        print(f"✅ Retrieved {len(workouts)} workouts for category {category_id}")
                    else:
                        print(f"❌ Get workouts failed: {response.text}")

                return all_categories
            else:
                print(f"❌ Get all categories failed: {response.text}")
                return []

        except Exception as e:
            print(f"❌ Get all categories error: {str(e)}")
            return []

    def test_replace_workout_charge(self, customer_id=1, division_id=1):
        """Test replacing workout charge (UPSERT logic)"""
        print(f"\n🔄 Testing Replace Workout Charge for Customer {customer_id}, Division {division_id}...")
        
        # Sample workout charge data
        workout_charge_data = {
            "customer_id": customer_id,
            "division_id": division_id,
            "workout_customer_charge_content": {
                "category_CAT001": {
                    "category_id": "CAT001",
                    "workouts": {
                        "workout_WORKOUT_FIT_001": {
                            "group_id": "WORKOUT_FIT_001",
                            "years": {
                                "2024": {
                                    "month_1": {"month": 1, "quantity": 10},
                                    "month_2": {"month": 2, "quantity": 12},
                                    "month_3": {"month": 3, "quantity": 15},
                                    "month_4": {"month": 4, "quantity": 8}
                                }
                            }
                        }
                    }
                },
                "category_CAT002": {
                    "category_id": "CAT002",
                    "workouts": {
                        "workout_WORKOUT_STR_001": {
                            "group_id": "WORKOUT_STR_001",
                            "years": {
                                "2024": {
                                    "month_1": {"month": 1, "quantity": 5},
                                    "month_2": {"month": 2, "quantity": 8},
                                    "month_3": {"month": 3, "quantity": 10}
                                }
                            }
                        }
                    }
                }
            }
        }
        
        try:
            response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/replace", 
                                   json=workout_charge_data, 
                                   headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                replaced_charge = data.get('data', {})
                charge_id = replaced_charge.get('id')
                print(f"✅ Workout charge replaced successfully with ID: {charge_id}")
                return charge_id, replaced_charge
            else:
                print(f"❌ Replace workout charge failed: {response.text}")
                return None, None
        except Exception as e:
            print(f"❌ Replace workout charge error: {str(e)}")
            return None, None
    
    def test_create_workout_charge(self, customer_id=2, division_id=2):
        """Test creating workout charge (should work for new customer/division)"""
        print(f"\n📝 Testing Create Workout Charge for Customer {customer_id}, Division {division_id}...")
        
        # Sample workout charge data for different customer/division
        workout_charge_data = {
            "customer_id": customer_id,
            "division_id": division_id,
            "workout_customer_charge_content": {
                "category_CAT003": {
                    "category_id": "CAT003",
                    "workouts": {
                        "workout_WORKOUT_CAR_001": {
                            "group_id": "WORKOUT_CAR_001",
                            "years": {
                                "2024": {
                                    "month_1": {"month": 1, "quantity": 20},
                                    "month_2": {"month": 2, "quantity": 22},
                                    "month_3": {"month": 3, "quantity": 25}
                                }
                            }
                        }
                    }
                }
            }
        }
        
        try:
            response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/", 
                                   json=workout_charge_data, 
                                   headers=self.headers)
            if response.status_code == 201:
                data = response.json()
                created_charge = data.get('data', {})
                charge_id = created_charge.get('id')
                print(f"✅ Workout charge created successfully with ID: {charge_id}")
                return charge_id, created_charge
            else:
                print(f"❌ Create workout charge failed: {response.text}")
                return None, None
        except Exception as e:
            print(f"❌ Create workout charge error: {str(e)}")
            return None, None
    
    def test_create_duplicate_workout_charge(self, customer_id=1, division_id=1):
        """Test creating duplicate workout charge (should fail)"""
        print(f"\n⚠️ Testing Create Duplicate Workout Charge for Customer {customer_id}, Division {division_id}...")
        
        # Try to create for same customer/division again
        workout_charge_data = {
            "customer_id": customer_id,
            "division_id": division_id,
            "workout_customer_charge_content": {
                "category_CAT004": {
                    "category_id": "CAT004",
                    "workouts": {
                        "workout_WORKOUT_YOG_001": {
                            "group_id": "WORKOUT_YOG_001",
                            "years": {
                                "2024": {
                                    "month_1": {"month": 1, "quantity": 5}
                                }
                            }
                        }
                    }
                }
            }
        }
        
        try:
            response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/", 
                                   json=workout_charge_data, 
                                   headers=self.headers)
            if response.status_code == 400:
                print("✅ Create duplicate correctly failed with 400 error")
                return True
            else:
                print(f"⚠️ Expected 400 error but got: {response.status_code} - {response.text}")
                return False
        except Exception as e:
            print(f"❌ Create duplicate error: {str(e)}")
            return False
    
    def test_get_workout_charges(self):
        """Test getting all workout charges"""
        print("\n📋 Testing Get All Workout Charges...")
        try:
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/", headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                charges = data.get('data', [])
                print(f"✅ Retrieved {len(charges)} workout charges")
                return charges
            else:
                print(f"❌ Get all workout charges failed: {response.text}")
                return []
        except Exception as e:
            print(f"❌ Get all workout charges error: {str(e)}")
            return []
    
    def test_get_workout_charge_by_customer_division(self, customer_id=1, division_id=1):
        """Test getting workout charge by customer and division"""
        print(f"\n🔍 Testing Get Workout Charge by Customer {customer_id}, Division {division_id}...")
        try:
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/customer/{customer_id}/division/{division_id}", 
                                  headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                charge = data.get('data', {})
                print(f"✅ Retrieved workout charge for customer/division")
                return charge
            else:
                print(f"❌ Get workout charge by customer/division failed: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Get workout charge by customer/division error: {str(e)}")
            return None
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Workout Customer Charge API Tests...")
        print("=" * 60)
        
        # Authenticate first
        if not self.authenticate():
            print("❌ Cannot proceed without authentication")
            return
        
        # Test dropdown data
        customers = self.test_get_dropdown_data()
        self.test_get_all_categories()

        # Test replace (UPSERT) - this should work regardless
        charge_id_1, replaced_charge = self.test_replace_workout_charge(1, 1)
        
        # Test create for new customer/division - should work
        charge_id_2, created_charge = self.test_create_workout_charge(2, 2)
        
        # Test create duplicate - should fail
        self.test_create_duplicate_workout_charge(1, 1)
        
        # Test get operations
        self.test_get_workout_charges()
        self.test_get_workout_charge_by_customer_division(1, 1)
        
        print("\n" + "=" * 60)
        print("🎉 All Workout Customer Charge API tests completed!")
        print("\n📝 Summary:")
        print("- ✅ Replace (UPSERT): Replaces all data for customer/division")
        print("- ✅ Create: Only works for new customer/division combinations")
        print("- ✅ Create prevents data loss by rejecting duplicates")
        print("- ✅ Use /replace endpoint for UPSERT behavior")

if __name__ == "__main__":
    tester = WorkoutChargeAPITester()
    tester.run_all_tests()
