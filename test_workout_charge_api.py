#!/usr/bin/env python3
"""
Test script for Workout Customer Charge API endpoints
Run this script to test all Workout Customer Charge API functionality
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:5000"
AUTH_ENDPOINT = f"{BASE_URL}/auth/login"
WORKOUT_CHARGE_ENDPOINT = f"{BASE_URL}/workout-charges"

# Test credentials (adjust as needed)
TEST_CREDENTIALS = {
    "username": "admin",
    "password": "admin123"
}

class WorkoutChargeAPITester:
    def __init__(self):
        self.token = None
        self.headers = {"Content-Type": "application/json"}
        
    def authenticate(self):
        """Get JWT token for authentication"""
        print("🔐 Authenticating...")
        try:
            response = requests.post(AUTH_ENDPOINT, json=TEST_CREDENTIALS)
            if response.status_code == 200:
                data = response.json()
                self.token = data.get('data', {}).get('access_token')
                self.headers["Authorization"] = f"Bearer {self.token}"
                print("✅ Authentication successful")
                return True
            else:
                print(f"❌ Authentication failed: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Authentication error: {str(e)}")
            return False
    
    def test_get_dropdown_data(self):
        """Test getting dropdown data"""
        print("\n📋 Testing Dropdown Data...")
        
        # Test get customers
        try:
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/customers", headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                customers = data.get('data', [])
                print(f"✅ Retrieved {len(customers)} customers for dropdown")
                return customers
            else:
                print(f"❌ Get customers failed: {response.text}")
                return []
        except Exception as e:
            print(f"❌ Get customers error: {str(e)}")
            return []
    
    def test_get_categories_and_workouts(self):
        """Test getting categories and workouts"""
        print("\n🏷️ Testing Categories and Workouts...")
        
        # Test get categories
        try:
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/categories", headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                categories = data.get('data', [])
                print(f"✅ Retrieved {len(categories)} categories")
                
                # Test get workouts for first category
                if categories:
                    category_id = categories[0]['id']
                    response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/categories/{category_id}/workouts", 
                                          headers=self.headers)
                    if response.status_code == 200:
                        data = response.json()
                        workouts = data.get('data', [])
                        print(f"✅ Retrieved {len(workouts)} workouts for category {category_id}")
                        return categories, workouts
                    else:
                        print(f"❌ Get workouts failed: {response.text}")
                        return categories, []
                
                return categories, []
            else:
                print(f"❌ Get categories failed: {response.text}")
                return [], []
        except Exception as e:
            print(f"❌ Get categories error: {str(e)}")
            return [], []
    
    def test_create_workout_charge(self, customer_id=1, division_id=1):
        """Test creating workout charge"""
        print(f"\n📝 Testing Create Workout Charge for Customer {customer_id}, Division {division_id}...")
        
        # Sample workout charge data
        workout_charge_data = {
            "customer_id": customer_id,
            "division_id": division_id,
            "workout_customer_charge_content": {
                "category_CAT001": {
                    "category_id": "CAT001",
                    "workouts": {
                        "workout_WORKOUT_FIT_001": {
                            "group_id": "WORKOUT_FIT_001",
                            "years": {
                                "2024": {
                                    "month_1": {"month": 1, "quantity": 10},
                                    "month_2": {"month": 2, "quantity": 12},
                                    "month_3": {"month": 3, "quantity": 15},
                                    "month_4": {"month": 4, "quantity": 8}
                                }
                            }
                        }
                    }
                },
                "category_CAT002": {
                    "category_id": "CAT002",
                    "workouts": {
                        "workout_WORKOUT_STR_001": {
                            "group_id": "WORKOUT_STR_001",
                            "years": {
                                "2024": {
                                    "month_1": {"month": 1, "quantity": 5},
                                    "month_2": {"month": 2, "quantity": 8},
                                    "month_3": {"month": 3, "quantity": 10}
                                }
                            }
                        }
                    }
                }
            }
        }
        
        try:
            response = requests.post(f"{WORKOUT_CHARGE_ENDPOINT}/", 
                                   json=workout_charge_data, 
                                   headers=self.headers)
            if response.status_code == 201:
                data = response.json()
                created_charge = data.get('data', {})
                charge_id = created_charge.get('id')
                print(f"✅ Workout charge created successfully with ID: {charge_id}")
                return charge_id, created_charge
            else:
                print(f"❌ Create workout charge failed: {response.text}")
                return None, None
        except Exception as e:
            print(f"❌ Create workout charge error: {str(e)}")
            return None, None
    
    def test_get_workout_charges(self):
        """Test getting all workout charges"""
        print("\n📋 Testing Get All Workout Charges...")
        try:
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/", headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                charges = data.get('data', [])
                print(f"✅ Retrieved {len(charges)} workout charges")
                return charges
            else:
                print(f"❌ Get all workout charges failed: {response.text}")
                return []
        except Exception as e:
            print(f"❌ Get all workout charges error: {str(e)}")
            return []
    
    def test_get_workout_charge_by_id(self, charge_id):
        """Test getting workout charge by ID"""
        if not charge_id:
            return None
            
        print(f"\n🔍 Testing Get Workout Charge by ID: {charge_id}...")
        try:
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/{charge_id}", 
                                  headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                charge = data.get('data', {})
                print(f"✅ Retrieved workout charge: {charge.get('id')}")
                return charge
            else:
                print(f"❌ Get workout charge by ID failed: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Get workout charge by ID error: {str(e)}")
            return None
    
    def test_get_workout_charge_by_customer_division(self, customer_id=1, division_id=1):
        """Test getting workout charge by customer and division"""
        print(f"\n🔍 Testing Get Workout Charge by Customer {customer_id}, Division {division_id}...")
        try:
            response = requests.get(f"{WORKOUT_CHARGE_ENDPOINT}/customer/{customer_id}/division/{division_id}", 
                                  headers=self.headers)
            if response.status_code == 200:
                data = response.json()
                charge = data.get('data', {})
                print(f"✅ Retrieved workout charge for customer/division")
                return charge
            else:
                print(f"❌ Get workout charge by customer/division failed: {response.text}")
                return None
        except Exception as e:
            print(f"❌ Get workout charge by customer/division error: {str(e)}")
            return None
    
    def test_update_workout_charge(self, charge_id, customer_id=1, division_id=1):
        """Test updating workout charge"""
        if not charge_id:
            return False
            
        print(f"\n✏️ Testing Update Workout Charge ID: {charge_id}...")
        
        # Updated data with different quantities
        update_data = {
            "customer_id": customer_id,
            "division_id": division_id,
            "workout_customer_charge_content": {
                "category_CAT001": {
                    "category_id": "CAT001",
                    "workouts": {
                        "workout_WORKOUT_FIT_001": {
                            "group_id": "WORKOUT_FIT_001",
                            "years": {
                                "2024": {
                                    "month_1": {"month": 1, "quantity": 20},  # Updated
                                    "month_2": {"month": 2, "quantity": 25},  # Updated
                                    "month_3": {"month": 3, "quantity": 30},  # Updated
                                    "month_5": {"month": 5, "quantity": 18}   # New month
                                }
                            }
                        }
                    }
                }
            }
        }
        
        try:
            response = requests.put(f"{WORKOUT_CHARGE_ENDPOINT}/{charge_id}", 
                                  json=update_data, 
                                  headers=self.headers)
            if response.status_code == 200:
                print("✅ Workout charge updated successfully")
                return True
            else:
                print(f"❌ Update workout charge failed: {response.text}")
                return False
        except Exception as e:
            print(f"❌ Update workout charge error: {str(e)}")
            return False
    
    def test_delete_workout_charge(self, charge_id):
        """Test deleting workout charge"""
        if not charge_id:
            return
            
        print(f"\n🗑️ Testing Delete Workout Charge ID: {charge_id}...")
        try:
            response = requests.delete(f"{WORKOUT_CHARGE_ENDPOINT}/{charge_id}", 
                                     headers=self.headers)
            if response.status_code == 200:
                print("✅ Workout charge deleted successfully")
            else:
                print(f"❌ Delete workout charge failed: {response.text}")
        except Exception as e:
            print(f"❌ Delete workout charge error: {str(e)}")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Workout Customer Charge API Tests...")
        
        # Authenticate first
        if not self.authenticate():
            print("❌ Cannot proceed without authentication")
            return
        
        # Test dropdown data
        customers = self.test_get_dropdown_data()
        categories, workouts = self.test_get_categories_and_workouts()
        
        # Test main CRUD operations
        charge_id, created_charge = self.test_create_workout_charge()
        self.test_get_workout_charges()
        
        if charge_id:
            self.test_get_workout_charge_by_id(charge_id)
            self.test_get_workout_charge_by_customer_division()
            self.test_update_workout_charge(charge_id)
            
            # Cleanup
            self.test_delete_workout_charge(charge_id)
        
        print("\n🎉 All Workout Customer Charge API tests completed!")

if __name__ == "__main__":
    tester = WorkoutChargeAPITester()
    tester.run_all_tests()
