# Customer API Implementation

Đây là implementation của Customer API dựa trên các function trong `service_code.ts`, đư<PERSON><PERSON> tích hợp vào Flask API hiện tại.

## 📁 Files đã tạo/cập nhật

### 1. Data Access Layer (DAO)
- `app/dao/customer_dao.py` - Chứa tất cả SQL queries cho Customer, Customer Settings, và Customer Divisions

### 2. Business Logic Layer (Service)
- `app/services/customer_service.py` - Chứa business logic và validation

### 3. API Layer (Controller)
- `app/controllers/customers_controller.py` - Chứa tất cả API endpoints
- `app/controllers/__init__.py` - Đ<PERSON> cập nhật để register Customer blueprint

### 4. Database
- `database/customer_tables.sql` - Script tạo bảng và sample data

### 5. Documentation & Testing
- `CUSTOMER_API_DOCUMENTATION.md` - Tài liệu chi tiết về API endpoints
- `test_customer_api.py` - Script test tự động
- `CUSTOMER_API_README.md` - File này

## 🚀 Cách chạy

### 1. Chuẩn bị Database
```sql
-- Chạy script tạo bảng
psql -d your_database -f database/customer_tables.sql
```

### 2. Cài đặt Dependencies
```bash
# Activate virtual environment
source venv/bin/activate  # Linux/Mac
# hoặc
venv\Scripts\activate     # Windows

# Install dependencies
pip install -r requirements.txt
```

### 3. Cấu hình Database
Đảm bảo file `.env` hoặc `config.py` có thông tin database đúng:
```
DB_USERNAME=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=5432
DB_NAME=your_database
```

### 4. Chạy API Server
```bash
python run.py
```

Server sẽ chạy tại: `http://localhost:5000`

## 📋 API Endpoints

### Customer Management
- `GET /customers/` - Lấy tất cả customers
- `GET /customers/{id}` - Lấy customer theo ID
- `POST /customers/` - Tạo customer mới
- `PUT /customers/{id}` - Cập nhật customer
- `DELETE /customers/{id}` - Xóa customer

### Customer Settings
- `GET /customers/{id}/settings` - Lấy settings của customer
- `POST /customers/{id}/settings` - Tạo/cập nhật settings

### Customer Divisions
- `GET /customers/{id}/divisions` - Lấy divisions của customer
- `GET /customers/divisions/{id}` - Lấy division theo ID
- `POST /customers/{id}/divisions` - Tạo division mới
- `PUT /customers/divisions/{id}` - Cập nhật division
- `DELETE /customers/divisions/{id}` - Xóa division

## 🧪 Testing

### 1. Automatic Testing
```bash
python test_customer_api.py
```

### 2. Manual Testing với curl

#### Đăng nhập để lấy token:
```bash
curl -X POST http://localhost:5000/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin123"}'
```

#### Lấy danh sách customers:
```bash
curl -X GET http://localhost:5000/customers/ \
  -H "Authorization: Bearer YOUR_TOKEN"
```

#### Tạo customer mới:
```bash
curl -X POST http://localhost:5000/customers/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "code": "CUST004",
    "name": "New Customer",
    "address": "123 New Street",
    "taxNumber": "*********",
    "phone": "0123456789"
  }'
```

## 🏗️ Architecture

### Mapping từ TypeScript sang Python

| TypeScript Function | Python Endpoint | HTTP Method |
|---------------------|-----------------|-------------|
| `getAllCustomers()` | `/customers/` | GET |
| `getCustomerById(id)` | `/customers/{id}` | GET |
| `addCustomer(data)` | `/customers/` | POST |
| `updateCustomer(data)` | `/customers/{id}` | PUT |
| `deleteCustomer(id)` | `/customers/{id}` | DELETE |
| `getCustomerSettings(id)` | `/customers/{id}/settings` | GET |
| `addOrUpdateCustomerSettings(data)` | `/customers/{id}/settings` | POST |
| `getCustomerDivisionsByCustomerId(id)` | `/customers/{id}/divisions` | GET |
| `getCustomerDivisionById(id)` | `/customers/divisions/{id}` | GET |
| `addCustomerDivision(data)` | `/customers/{id}/divisions` | POST |
| `updateCustomerDivision(data)` | `/customers/divisions/{id}` | PUT |
| `deleteCustomerDivision(id)` | `/customers/divisions/{id}` | DELETE |

### Database Schema

```sql
customers (
  id, code, name, address, tax_number, phone, status,
  created_at, updated_at
)

customer_settings (
  id, customer_id, master_data_import_mapping (JSONB),
  created_at, updated_at
)

customer_divisions (
  id, code, name, description, customer_id, status,
  created_at, updated_at
)
```

## 🔒 Security

- Tất cả endpoints đều yêu cầu JWT authentication
- Validation đầy đủ cho input data
- SQL injection protection thông qua parameterized queries
- Business logic validation (không được xóa active records)

## 🎯 Features

- ✅ CRUD operations cho Customers
- ✅ Customer Settings management (UPSERT)
- ✅ Customer Divisions management
- ✅ Dynamic field updates
- ✅ Status-based deletion protection
- ✅ JSON response format nhất quán
- ✅ Error handling đầy đủ
- ✅ Database transactions
- ✅ Automatic timestamps
- ✅ Foreign key constraints

## 🔧 Troubleshooting

### Lỗi Database Connection
- Kiểm tra thông tin database trong `config.py`
- Đảm bảo PostgreSQL đang chạy
- Kiểm tra permissions của user database

### Lỗi Authentication
- Đảm bảo đã seed users: `POST /user/seed`
- Kiểm tra JWT token có hợp lệ không
- Kiểm tra header Authorization format

### Lỗi Import
- Đảm bảo đã activate virtual environment
- Chạy `pip install -r requirements.txt`
- Kiểm tra Python path

## 📝 Notes

- API này follow pattern của source code hiện tại
- Sử dụng raw SQL queries thay vì ORM
- Database-first approach
- Consistent error handling và response format
- Support cho PostgreSQL JSONB cho customer settings
