-- Customer Management Tables
-- Run this script to create the necessary tables for Customer API

-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50) UNIQUE,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    tax_number VARCHAR(50),
    phone VARCHAR(20),
    status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'active', 'inactive')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create customer_settings table
CREATE TABLE IF NOT EXISTS customer_settings (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER NOT NULL UNIQUE,
    master_data_import_mapping JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- Create customer_divisions table
CREATE TABLE IF NOT EXISTS customer_divisions (
    id SERIAL PRIMARY KEY,
    code VARCHAR(50),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    customer_id INTEGER NOT NULL,
    status VARCHAR(20) DEFAULT 'new' CHECK (status IN ('new', 'active', 'inactive')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(code);
CREATE INDEX IF NOT EXISTS idx_customers_status ON customers(status);
CREATE INDEX IF NOT EXISTS idx_customer_settings_customer_id ON customer_settings(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_divisions_customer_id ON customer_divisions(customer_id);
CREATE INDEX IF NOT EXISTS idx_customer_divisions_code ON customer_divisions(code);
CREATE INDEX IF NOT EXISTS idx_customer_divisions_status ON customer_divisions(status);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables
DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
CREATE TRIGGER update_customers_updated_at 
    BEFORE UPDATE ON customers 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_settings_updated_at ON customer_settings;
CREATE TRIGGER update_customer_settings_updated_at 
    BEFORE UPDATE ON customer_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_customer_divisions_updated_at ON customer_divisions;
CREATE TRIGGER update_customer_divisions_updated_at 
    BEFORE UPDATE ON customer_divisions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing
INSERT INTO customers (code, name, address, tax_number, phone, status) VALUES
('CUST001', 'ABC Company Ltd', '123 Business Street, City', '1234567890', '******-0001', 'active'),
('CUST002', 'XYZ Corporation', '456 Corporate Ave, Town', '0987654321', '******-0002', 'active'),
('CUST003', 'Test Customer Inc', '789 Test Road, Village', '1122334455', '******-0003', 'inactive')
ON CONFLICT (code) DO NOTHING;

-- Insert sample customer settings
INSERT INTO customer_settings (customer_id, master_data_import_mapping) VALUES
(1, '{"productCode": "sku", "productName": "title", "price": "cost"}'),
(2, '{"itemCode": "product_id", "itemName": "product_name", "unitPrice": "price"}')
ON CONFLICT (customer_id) DO NOTHING;

-- Insert sample customer divisions
INSERT INTO customer_divisions (code, name, description, customer_id, status) VALUES
('DIV001', 'Sales Division', 'Handles all sales operations', 1, 'active'),
('DIV002', 'Marketing Division', 'Manages marketing campaigns', 1, 'active'),
('DIV003', 'IT Division', 'Information Technology department', 2, 'active'),
('DIV004', 'HR Division', 'Human Resources department', 2, 'inactive');

-- Grant permissions (adjust as needed for your setup)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON customers TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON customer_settings TO your_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON customer_divisions TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE customers_id_seq TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE customer_settings_id_seq TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE customer_divisions_id_seq TO your_app_user;

-- Display table information
SELECT 'customers' as table_name, COUNT(*) as record_count FROM customers
UNION ALL
SELECT 'customer_settings' as table_name, COUNT(*) as record_count FROM customer_settings
UNION ALL
SELECT 'customer_divisions' as table_name, COUNT(*) as record_count FROM customer_divisions;

COMMENT ON TABLE customers IS 'Stores customer information';
COMMENT ON TABLE customer_settings IS 'Stores customer-specific settings and configurations';
COMMENT ON TABLE customer_divisions IS 'Stores customer divisions/departments information';

COMMENT ON COLUMN customers.status IS 'Customer status: new, active, inactive';
COMMENT ON COLUMN customer_settings.master_data_import_mapping IS 'JSON mapping for data import configuration';
COMMENT ON COLUMN customer_divisions.status IS 'Division status: new, active, inactive';
