from flask import Blueprint, request, jsonify
from ..services.customer_service import (
    get_all_customers,
    get_customer_by_id,
    add_customer,
    update_customer,
    delete_customer,
    get_customer_divisions_by_customer_id,
    get_customer_division_by_id,
    add_customer_division,
    update_customer_division,
    delete_customer_division,
)

customer_bp = Blueprint('customer', __name__)

@customer_bp.route('/customers', methods=['GET'])
def get_all_customers_api():
    try:
        customers = get_all_customers()
        return jsonify({"status": "success", "data": customers}), 200
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@customer_bp.route('/customers/<int:id>', methods=['GET'])
def get_customer_by_id_api(id):
    try:
        customer = get_customer_by_id(id)
        if customer:
            return jsonify({"status": "success", "data": customer}), 200
        return jsonify({"status": "error", "message": "Customer not found"}), 404
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@customer_bp.route('/customers', methods=['POST'])
def add_customer_api():
    try:
        customer_data = request.get_json()
        add_customer(customer_data)
        return jsonify({"status": "success", "message": "Customer added successfully"}), 201
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@customer_bp.route('/customers/<int:id>', methods=['PUT'])
def update_customer_api(id):
    try:
        customer_data = request.get_json()
        customer_data['id'] = id
        update_customer(customer_data)
        return jsonify({"status": "success", "message": "Customer updated successfully"}), 200
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@customer_bp.route('/customers/<int:id>', methods=['DELETE'])
def delete_customer_api(id):
    try:
        delete_customer(id)
        return jsonify({"status": "success", "message": "Customer deleted successfully"}), 200
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@customer_bp.route('/customers/<int:customer_id>/divisions', methods=['GET'])
def get_customer_divisions_by_customer_id_api(customer_id):
    try:
        divisions = get_customer_divisions_by_customer_id(customer_id)
        return jsonify({"status": "success", "data": divisions}), 200
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@customer_bp.route('/divisions/<int:id>', methods=['GET'])
def get_customer_division_by_id_api(id):
    try:
        division = get_customer_division_by_id(id)
        if division:
            return jsonify({"status": "success", "data": division}), 200
        return jsonify({"status": "error", "message": "Division not found"}), 404
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@customer_bp.route('/divisions', methods=['POST'])
def add_customer_division_api():
    try:
        division_data = request.get_json()
        add_customer_division(division_data)
        return jsonify({"status": "success", "message": "Division added successfully"}), 201
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@customer_bp.route('/divisions/<int:id>', methods=['PUT'])
def update_customer_division_api(id):
    try:
        division_data = request.get_json()
        division_data['id'] = id
        update_customer_division(division_data)
        return jsonify({"status": "success", "message": "Division updated successfully"}), 200
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500

@customer_bp.route('/divisions/<int:id>', methods=['DELETE'])
def delete_customer_division_api(id):
    try:
        delete_customer_division(id)
        return jsonify({"status": "success", "message": "Division deleted successfully"}), 200
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)}), 500
