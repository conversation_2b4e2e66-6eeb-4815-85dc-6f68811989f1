import { useSQLite } from '../../../hooks/useSQLite'
import type {
  WorkoutCustomerCharge,
  WorkoutCustomerChargeFlat,
  WorkoutCustomerChargeFormData,
  CategoryOption,
  CategoryWorkoutOption,
  CustomerOption,
  DivisionOption,
} from '../../../types/WorkoutCustomerCharge'

// Helper function to generate unique ID
const generateWorkoutChargeId = (): string => {
  return `WCC_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

// Helper function to get table name from useSQLite config
const getWorkoutCustomerChargeTableName = () => {
  return 'workout_customer_charge' // Use direct table name like customer service
}

// Manual database check function for debugging
export const checkWorkoutCustomerChargeTable = async (): Promise<void> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getWorkoutCustomerChargeTableName()

    // Check if table exists
    const tableExists = await executeQuery(
      `SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`,
    )
    console.log('Table exists check:', tableExists)

    // Get table schema
    const schema = await executeQuery(`PRAGMA table_info(${tableName})`)
    console.log('Table schema:', schema)

    // Count all records
    const count = await executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`)
    console.log('Total records:', count)

    // Get all records
    const allRecords = await executeQuery(`SELECT * FROM ${tableName}`)
    console.log('All records:', allRecords)
  } catch (error) {
    console.error('Error checking table:', error)
  }
}

// Ensure Workout Customer Charge table exists (without dropping existing data)
export const ensureWorkoutCustomerChargeTableExists = async (): Promise<void> => {
  const { executeQuery } = useSQLite()

  try {
    console.log('Ensuring Workout Customer Charge table exists...')
    const tableName = getWorkoutCustomerChargeTableName()
    console.log('Workout Customer Charge table name:', tableName)

    // Create table only if it doesn't exist - NEVER drop existing data
    console.log('Creating workout_customer_charge table if not exists (preserving data)...')
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id TEXT PRIMARY KEY,
        customer_id INTEGER NOT NULL,
        division_id INTEGER NULL,
        category_id TEXT NOT NULL,
        workout_id TEXT NOT NULL,
        year INTEGER NOT NULL,
        month_1 INTEGER NULL,
        month_2 INTEGER NULL,
        month_3 INTEGER NULL,
        month_4 INTEGER NULL,
        month_5 INTEGER NULL,
        month_6 INTEGER NULL,
        month_7 INTEGER NULL,
        month_8 INTEGER NULL,
        month_9 INTEGER NULL,
        month_10 INTEGER NULL,
        month_11 INTEGER NULL,
        month_12 INTEGER NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `
    console.log('Create table SQL:', createTableSQL)
    const createResult = await executeQuery(createTableSQL)
    console.log('Create table result:', createResult)

    // Enable WAL mode for better persistence
    try {
      await executeQuery('PRAGMA journal_mode=WAL')
      console.log('Enabled WAL mode for better data persistence')
    } catch (walError) {
      console.log('Could not enable WAL mode:', walError)
    }

    // Verify table exists
    const schemaCheck = await executeQuery(`PRAGMA table_info(${tableName})`)
    console.log('Table schema verification:', schemaCheck)

    console.log('Workout Customer Charge table ensured to exist with data preservation')
  } catch (error) {
    console.error('Error ensuring Workout Customer Charge table exists:', error)
    throw error
  }
}

// Initialize Workout Customer Charge tables
export const initializeWorkoutCustomerChargeDB = async (): Promise<void> => {
  const { executeQuery } = useSQLite()

  try {
    console.log('Initializing Workout Customer Charge tables...')
    const tableName = getWorkoutCustomerChargeTableName()
    console.log('Workout Customer Charge table name:', tableName)

    // Drop existing table if it exists (to update schema)
    console.log('Dropping existing workout_customer_charge table if exists...')
    try {
      await executeQuery(`DROP TABLE IF EXISTS ${tableName}`)
      console.log('Existing table dropped successfully')
    } catch (dropError) {
      console.log('No existing table to drop or error dropping:', dropError)
    }

    // Create workout_customer_charge table only if it doesn't exist
    console.log('Creating workout_customer_charge table...')
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id TEXT PRIMARY KEY,
        customer_id INTEGER NOT NULL,
        division_id INTEGER NOT NULL,
        workout_customer_charge_content TEXT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
        FOREIGN KEY (division_id) REFERENCES customer_divisions (id) ON DELETE CASCADE
      )
    `
    console.log('Create table SQL:', createTableSQL)
    const createResult = await executeQuery(createTableSQL)
    console.log('Create table result:', createResult)

    console.log('Workout Customer Charge database initialized successfully')
  } catch (error) {
    console.error('Error initializing Workout Customer Charge database:', error)
    throw error
  }
}

// Get Workout Customer Charge by ID
export const getWorkoutCustomerChargeById = async (
  id: string,
): Promise<WorkoutCustomerCharge | null> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getWorkoutCustomerChargeTableName()

    const result = await executeQuery(
      `
      SELECT
        wcc.id,
        wcc.customer_id,
        wcc.division_id,
        wcc.workout_customer_charge_content,
        wcc.created_at,
        wcc.updated_at
      FROM ${tableName} wcc
      WHERE wcc.id = ?
    `,
      [id],
    )

    if (!result?.result?.resultRows?.[0]) {
      return null
    }

    const row = result.result.resultRows[0]
    const contentJson = row[3] as string
    let parsedContent = {}

    try {
      parsedContent = JSON.parse(contentJson)
    } catch (parseError) {
      console.warn('Failed to parse workout_customer_charge_content:', parseError)
      parsedContent = {}
    }

    return {
      id: row[0] as string,
      customer_id: row[1] as number,
      division_id: row[2] as number,
      workout_customer_charge_content: parsedContent,
      created_at: row[4] as string,
      updated_at: row[5] as string,
    }
  } catch (error) {
    console.error('Error getting workout customer charge by ID:', error)
    throw error
  }
}

// Get all Workout Customer Charges with optional filters
export const getWorkoutCustomerCharges = async (filters?: {
  customer_id?: number
  division_id?: number
}): Promise<WorkoutCustomerCharge[]> => {
  const { executeQuery } = useSQLite()

  try {
    console.log('Getting workout customer charges with filters:', filters)
    const tableName = getWorkoutCustomerChargeTableName()

    let whereClause = ''
    const params: any[] = []

    if (filters) {
      const conditions: string[] = []

      if (filters.customer_id) {
        conditions.push('customer_id = ?')
        params.push(filters.customer_id)
      }

      if (filters.division_id) {
        conditions.push('division_id = ?')
        params.push(filters.division_id)
      }

      if (conditions.length > 0) {
        whereClause = `WHERE ${conditions.join(' AND ')}`
      }
    }

    const result = await executeQuery(
      `
      SELECT
        id,
        customer_id,
        division_id,
        category_id,
        workout_id,
        year,
        month_1, month_2, month_3, month_4,
        month_5, month_6, month_7, month_8,
        month_9, month_10, month_11, month_12,
        created_at,
        updated_at
      FROM ${tableName}
      ${whereClause}
      ORDER BY created_at DESC
    `,
      params,
    )

    console.log('getWorkoutCustomerCharges query result:', result)

    if (!result?.result?.resultRows || result.result.resultRows.length === 0) {
      console.log('No workout customer charges found')
      return []
    }

    console.log('Found', result.result.resultRows.length, 'flat records for list')

    // Convert flat records to hierarchical structure
    const flatRecords: WorkoutCustomerChargeFlat[] = result.result.resultRows.map((row) => ({
      id: row[0] as string,
      customer_id: row[1] as number,
      division_id: row[2] as number,
      category_id: row[3] as string,
      workout_id: row[4] as string,
      year: row[5] as number,
      month_1: row[6] as number | null,
      month_2: row[7] as number | null,
      month_3: row[8] as number | null,
      month_4: row[9] as number | null,
      month_5: row[10] as number | null,
      month_6: row[11] as number | null,
      month_7: row[12] as number | null,
      month_8: row[13] as number | null,
      month_9: row[14] as number | null,
      month_10: row[15] as number | null,
      month_11: row[16] as number | null,
      month_12: row[17] as number | null,
      created_at: row[18] as string,
      updated_at: row[19] as string,
    }))

    // Group by customer/division and convert to hierarchical
    const groupedByCustomerDivision: { [key: string]: WorkoutCustomerCharge } = {}

    flatRecords.forEach((record) => {
      const key = `${record.customer_id}_${record.division_id || 'null'}`

      if (!groupedByCustomerDivision[key]) {
        groupedByCustomerDivision[key] = {
          id: record.id,
          customer_id: record.customer_id,
          division_id: record.division_id || 0,
          workout_customer_charge_content: {},
          created_at: record.created_at,
          updated_at: record.updated_at,
        }
      }

      const charge = groupedByCustomerDivision[key]
      const categoryKey = `category_${record.category_id}`
      const workoutKey = `workout_${record.workout_id}`
      const yearKey = record.year.toString()

      // Initialize category if not exists
      if (!charge.workout_customer_charge_content[categoryKey]) {
        charge.workout_customer_charge_content[categoryKey] = {
          category_id: record.category_id,
          category_name: '', // Will be populated from categories table
          workouts: {},
        }
      }

      // Initialize workout if not exists
      if (!charge.workout_customer_charge_content[categoryKey].workouts[workoutKey]) {
        charge.workout_customer_charge_content[categoryKey].workouts[workoutKey] = {
          group_id: record.workout_id,
          group_name: '', // Will be populated from workouts table
          unit_price: 0, // Will be populated from workouts table
          years: {},
        }
      }

      // Initialize year if not exists
      if (
        !charge.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[yearKey]
      ) {
        charge.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[yearKey] = {}
      }

      // Add month data
      console.log('Adding month data for record:', record.id)
      for (let month = 1; month <= 12; month++) {
        const monthKey = `month_${month}`
        const quantity = record[`month_${month}` as keyof WorkoutCustomerChargeFlat] as
          | number
          | null
        if (quantity !== null && quantity > 0) {
          charge.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[yearKey][
            monthKey
          ] = {
            month: month,
            quantity: quantity,
          }
          console.log(`Added month ${month} with quantity ${quantity} for key ${key}`)
        }
      }
    })

    const finalResult = Object.values(groupedByCustomerDivision)
    console.log('Final list result:', finalResult.length, 'workout charges')
    finalResult.forEach((charge, index) => {
      console.log(`Charge ${index}:`, {
        id: charge.id,
        customer_id: charge.customer_id,
        division_id: charge.division_id,
        categories: Object.keys(charge.workout_customer_charge_content).length,
      })
    })
    return finalResult
  } catch (error) {
    console.error('Error getting workout customer charges:', error)
    throw error
  }
}

// Get existing Workout Customer Charge for customer/division
export const getWorkoutCustomerChargeByCustomerDivision = async (
  customerId: number,
  divisionId: number | null,
): Promise<WorkoutCustomerCharge | null> => {
  const { executeQuery } = useSQLite()

  try {
    console.log('Getting workout customer charge for customer/division:', customerId, divisionId)
    const tableName = getWorkoutCustomerChargeTableName()

    const whereClause =
      divisionId === null
        ? 'WHERE customer_id = ? AND division_id IS NULL'
        : 'WHERE customer_id = ? AND division_id = ?'

    const params = divisionId === null ? [customerId] : [customerId, divisionId]

    const result = await executeQuery(
      `
      SELECT
        id,
        customer_id,
        division_id,
        category_id,
        workout_id,
        year,
        month_1, month_2, month_3, month_4,
        month_5, month_6, month_7, month_8,
        month_9, month_10, month_11, month_12,
        created_at,
        updated_at
      FROM ${tableName}
      ${whereClause}
    `,
      params,
    )

    console.log('Query result:', result)

    if (!result?.result?.resultRows || result.result.resultRows.length === 0) {
      console.log('No workout customer charge found for customer/division:', customerId, divisionId)
      return null
    }

    console.log('Found', result.result.resultRows.length, 'flat records')

    // Convert flat records to hierarchical structure
    const flatRecords: WorkoutCustomerChargeFlat[] = result.result.resultRows.map((row) => ({
      id: row[0] as string,
      customer_id: row[1] as number,
      division_id: row[2] as number,
      category_id: row[3] as string,
      workout_id: row[4] as string,
      year: row[5] as number,
      month_1: row[6] as number | null,
      month_2: row[7] as number | null,
      month_3: row[8] as number | null,
      month_4: row[9] as number | null,
      month_5: row[10] as number | null,
      month_6: row[11] as number | null,
      month_7: row[12] as number | null,
      month_8: row[13] as number | null,
      month_9: row[14] as number | null,
      month_10: row[15] as number | null,
      month_11: row[16] as number | null,
      month_12: row[17] as number | null,
      created_at: row[18] as string,
      updated_at: row[19] as string,
    }))

    console.log('Flat records:', flatRecords)

    // Convert to hierarchical structure
    const hierarchicalData: WorkoutCustomerCharge = {
      id: flatRecords[0].id,
      customer_id: flatRecords[0].customer_id,
      division_id: flatRecords[0].division_id || 0,
      workout_customer_charge_content: {},
      created_at: flatRecords[0].created_at,
      updated_at: flatRecords[0].updated_at,
    }

    console.log('Converting flat records to hierarchical structure...')
    for (const record of flatRecords) {
      console.log('Processing flat record:', record)
      const categoryKey = `category_${record.category_id}`
      const workoutKey = `workout_${record.workout_id}`
      const yearKey = record.year.toString()

      // Initialize category if not exists
      if (!hierarchicalData.workout_customer_charge_content[categoryKey]) {
        // Try to get category name from database
        let categoryName = ''
        try {
          const categoryResult = await executeQuery(`SELECT name FROM categories WHERE id = ?`, [
            record.category_id,
          ])
          if (categoryResult?.result?.resultRows?.[0]) {
            categoryName = categoryResult.result.resultRows[0][0] as string
          }
        } catch (error) {
          console.log('Could not load category name for:', record.category_id)
        }

        hierarchicalData.workout_customer_charge_content[categoryKey] = {
          category_id: record.category_id,
          category_name: categoryName,
          workouts: {},
        }
      }

      // Initialize workout if not exists
      if (!hierarchicalData.workout_customer_charge_content[categoryKey].workouts[workoutKey]) {
        // Try to get workout name and price from database
        let workoutName = ''
        let unitPrice = 0
        try {
          const workoutQueries = [
            `SELECT name, unit_price FROM workout WHERE id = ?`,
            `SELECT name, price as unit_price FROM workout WHERE id = ?`,
            `SELECT name, unit_price FROM workouts WHERE id = ?`,
            `SELECT name, price as unit_price FROM workouts WHERE id = ?`,
          ]

          for (const query of workoutQueries) {
            try {
              const workoutResult = await executeQuery(query, [record.workout_id])
              if (workoutResult?.result?.resultRows?.[0]) {
                workoutName = workoutResult.result.resultRows[0][0] as string
                unitPrice = (workoutResult.result.resultRows[0][1] as number) || 0
                break
              }
            } catch (queryError) {
              continue
            }
          }
        } catch (error) {
          console.log('Could not load workout name for:', record.workout_id)
        }

        hierarchicalData.workout_customer_charge_content[categoryKey].workouts[workoutKey] = {
          group_id: record.workout_id,
          group_name: workoutName,
          unit_price: unitPrice,
          years: {},
        }
      }

      // Initialize year if not exists
      if (
        !hierarchicalData.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[
          yearKey
        ]
      ) {
        hierarchicalData.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[
          yearKey
        ] = {}
      }

      // Add month data
      console.log('Adding month data for record:', record.id)
      for (let month = 1; month <= 12; month++) {
        const monthKey = `month_${month}`
        const quantity = record[`month_${month}` as keyof WorkoutCustomerChargeFlat] as
          | number
          | null
        console.log(`Month ${month}: quantity = ${quantity}`)
        if (quantity !== null && quantity > 0) {
          hierarchicalData.workout_customer_charge_content[categoryKey].workouts[workoutKey].years[
            yearKey
          ][monthKey] = {
            month: month,
            quantity: quantity,
          }
          console.log(`Added month ${month} with quantity ${quantity}`)
        }
      }
    }

    console.log('Final hierarchical data:', JSON.stringify(hierarchicalData, null, 2))
    return hierarchicalData
  } catch (error) {
    console.error('Error getting workout customer charge by customer/division:', error)
    throw error
  }
}

// Create new Workout Customer Charge
export const createWorkoutCustomerCharge = async (
  data: WorkoutCustomerChargeFormData,
): Promise<WorkoutCustomerCharge> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getWorkoutCustomerChargeTableName()
    const now = new Date().toISOString()

    console.log('Creating workout customer charge with data:', data)

    // Ensure table exists and is properly configured
    console.log('Ensuring table exists and is properly configured...')
    console.log('Table name from config:', tableName)

    // Check if table exists and has correct schema
    const tableCheckResult = await executeQuery(
      `SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`,
    )
    console.log('Table verification result:', tableCheckResult)

    if (!tableCheckResult?.result?.resultRows || tableCheckResult.result.resultRows.length === 0) {
      console.log('Table does not exist, creating with correct schema...')
    } else {
      // Check if table has correct schema (category_id should be TEXT)
      console.log('Table exists, checking schema...')
      try {
        const schemaCheck = await executeQuery(`PRAGMA table_info(${tableName})`)
        console.log('Current table schema:', schemaCheck?.result?.resultRows)

        // Check if category_id column exists and is TEXT
        const categoryIdColumn = schemaCheck?.result?.resultRows?.find(
          (row) => row[1] === 'category_id',
        )
        console.log('Category ID column info:', categoryIdColumn)

        if (!categoryIdColumn || categoryIdColumn[2] !== 'TEXT') {
          console.log('Table has incorrect schema for category_id, recreating...')
          // Drop and recreate table with correct schema
          await executeQuery(`DROP TABLE IF EXISTS ${tableName}`)
          console.log('Old table dropped')
        } else {
          console.log('Table has correct schema')
        }
      } catch (schemaError) {
        console.error('Schema check failed, recreating table:', schemaError)
        await executeQuery(`DROP TABLE IF EXISTS ${tableName}`)
      }
    }

    // Create table with correct schema
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS ${tableName} (
        id TEXT PRIMARY KEY,
        customer_id INTEGER NOT NULL,
        division_id INTEGER NULL,
        category_id TEXT NOT NULL,
        workout_id TEXT NOT NULL,
        year INTEGER NOT NULL,
        month_1 INTEGER NULL,
        month_2 INTEGER NULL,
        month_3 INTEGER NULL,
        month_4 INTEGER NULL,
        month_5 INTEGER NULL,
        month_6 INTEGER NULL,
        month_7 INTEGER NULL,
        month_8 INTEGER NULL,
        month_9 INTEGER NULL,
        month_10 INTEGER NULL,
        month_11 INTEGER NULL,
        month_12 INTEGER NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `

    const createResult = await executeQuery(createTableSQL)
    console.log('Table creation result:', createResult)

    // Verify table was created successfully
    const finalCheckResult = await executeQuery(
      `SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`,
    )
    console.log('Final table verification result:', finalCheckResult)

    if (!finalCheckResult?.result?.resultRows || finalCheckResult.result.resultRows.length === 0) {
      throw new Error(`Failed to create table ${tableName}`)
    } else {
      // Check if table has correct schema
      console.log('Table exists, checking schema...')
      try {
        const schemaCheck = await executeQuery(`PRAGMA table_info(${tableName})`)
        console.log('Current table schema:', schemaCheck)

        // Check if category_id column exists
        const hasCorrectSchema = schemaCheck?.result?.resultRows?.some(
          (row) => row[1] === 'category_id',
        )

        if (!hasCorrectSchema) {
          console.log('Table has incorrect schema, but preserving existing data')
          console.log('Schema differences will be handled by ALTER TABLE if needed')
        } else {
          console.log('Table has correct schema, preserving existing data')
        }
      } catch (schemaError) {
        console.error('Schema check failed, but preserving existing data:', schemaError)
      }
    }

    // Test database connection first
    try {
      const testResult = await executeQuery('SELECT 1 as test')
      console.log('Database connection test:', testResult)
    } catch (dbError) {
      console.error('Database connection failed:', dbError)
      throw new Error('Database connection failed')
    }

    // Check if table exists, if not create it
    try {
      const tableCheckResult = await executeQuery(
        `SELECT name FROM sqlite_master WHERE type='table' AND name='${tableName}'`,
      )
      console.log('Table check result:', tableCheckResult)

      if (
        !tableCheckResult?.result?.resultRows ||
        tableCheckResult.result.resultRows.length === 0
      ) {
        console.log('Table does not exist, creating it...')
        // await forceReinitializeWorkoutCustomerChargeDB()
      }
    } catch (tableError) {
      console.error('Table check failed:', tableError)
      console.log('Attempting to create table...')
      // await forceReinitializeWorkoutCustomerChargeDB()
    }

    // Validate input data
    if (!data.customer_id) {
      throw new Error('Invalid input data: customer_id is required')
    }

    if (
      !data.workout_customer_charge_content ||
      Object.keys(data.workout_customer_charge_content).length === 0
    ) {
      throw new Error('Invalid input data: workout_customer_charge_content is required')
    }

    // Don't delete any existing data - we'll use UPSERT logic instead
    console.log('Using UPSERT logic - no data will be deleted')

    // Convert hierarchical data to flat records
    const flatRecords: WorkoutCustomerChargeFlat[] = []

    console.log('Converting hierarchical data to flat records...')
    console.log(
      'Input data.workout_customer_charge_content:',
      JSON.stringify(data.workout_customer_charge_content, null, 2),
    )

    Object.entries(data.workout_customer_charge_content).forEach(
      ([categoryKey, categoryData]: [string, any]) => {
        console.log('Processing category:', categoryKey, categoryData)
        Object.entries(categoryData.workouts || {}).forEach(
          ([workoutKey, workoutData]: [string, any]) => {
            console.log('Processing workout:', workoutKey, workoutData)
            Object.entries(workoutData.years || {}).forEach(([year, yearData]: [string, any]) => {
              console.log('Processing year:', year, yearData)
              const record: WorkoutCustomerChargeFlat = {
                id: generateWorkoutChargeId(),
                customer_id: data.customer_id,
                division_id: data.division_id,
                category_id: categoryData.category_id,
                workout_id: workoutData.group_id,
                year: parseInt(year),
                month_1: null,
                month_2: null,
                month_3: null,
                month_4: null,
                month_5: null,
                month_6: null,
                month_7: null,
                month_8: null,
                month_9: null,
                month_10: null,
                month_11: null,
                month_12: null,
                created_at: now,
                updated_at: now,
              }

              // Set month quantities
              console.log('Setting month quantities for year data:', yearData)
              Object.entries(yearData).forEach(([monthKey, monthData]: [string, any]) => {
                console.log('Processing month:', monthKey, monthData)
                const monthNum = monthData.month
                if (monthNum >= 1 && monthNum <= 12) {
                  ;(record as any)[`month_${monthNum}`] = monthData.quantity || 0
                  console.log(`Set month_${monthNum} = ${monthData.quantity || 0}`)
                }
              })

              console.log('Final record before push:', record)
              flatRecords.push(record)
            })
          },
        )
      },
    )

    // Insert all flat records with transaction
    console.log('Inserting flat records:', flatRecords.length, 'records')

    if (flatRecords.length === 0) {
      throw new Error('No records to insert - check data conversion')
    }

    // Start transaction
    await executeQuery('BEGIN TRANSACTION')
    console.log('Started database transaction')

    try {
      for (const record of flatRecords) {
        console.log('Processing record (UPSERT):', record)

        // Check if record exists
        const existingRecordResult = await executeQuery(
          `SELECT id FROM ${tableName} WHERE customer_id = ? AND division_id = ? AND category_id = ? AND workout_id = ? AND year = ?`,
          [
            record.customer_id,
            record.division_id,
            record.category_id,
            record.workout_id,
            record.year,
          ],
        )

        let upsertResult
        if (
          existingRecordResult?.result?.resultRows &&
          existingRecordResult.result.resultRows.length > 0
        ) {
          // Record exists - UPDATE
          console.log('Record exists, updating...')
          upsertResult = await executeQuery(
            `
            UPDATE ${tableName} SET
              month_1 = ?, month_2 = ?, month_3 = ?, month_4 = ?, month_5 = ?, month_6 = ?,
              month_7 = ?, month_8 = ?, month_9 = ?, month_10 = ?, month_11 = ?, month_12 = ?,
              updated_at = ?
            WHERE customer_id = ? AND division_id = ? AND category_id = ? AND workout_id = ? AND year = ?
          `,
            [
              record.month_1,
              record.month_2,
              record.month_3,
              record.month_4,
              record.month_5,
              record.month_6,
              record.month_7,
              record.month_8,
              record.month_9,
              record.month_10,
              record.month_11,
              record.month_12,
              record.updated_at,
              record.customer_id,
              record.division_id,
              record.category_id,
              record.workout_id,
              record.year,
            ],
          )
        } else {
          // Record doesn't exist - INSERT
          console.log('Record does not exist, inserting...')
          upsertResult = await executeQuery(
            `
            INSERT INTO ${tableName} (
              id, customer_id, division_id, category_id, workout_id, year,
              month_1, month_2, month_3, month_4, month_5, month_6,
              month_7, month_8, month_9, month_10, month_11, month_12,
              created_at, updated_at
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `,
            [
              record.id,
              record.customer_id,
              record.division_id,
              record.category_id,
              record.workout_id,
              record.year,
              record.month_1,
              record.month_2,
              record.month_3,
              record.month_4,
              record.month_5,
              record.month_6,
              record.month_7,
              record.month_8,
              record.month_9,
              record.month_10,
              record.month_11,
              record.month_12,
              record.created_at,
              record.updated_at,
            ],
          )
        }

        console.log('UPSERT result:', upsertResult)

        if (!upsertResult || upsertResult.type !== 'exec') {
          console.error('Failed to upsert record:', record)
          throw new Error('Failed to upsert workout customer charge record')
        } else {
          console.log('Successfully upserted record with ID:', record.id)

          // Immediate verification for this record
          const verifySQL = `SELECT id FROM ${tableName} WHERE id = ?`
          const verifyResult = await executeQuery(verifySQL, [record.id])
          const recordExists = verifyResult?.result?.resultRows?.length > 0
          console.log(
            'Immediate verification for record:',
            record.id,
            recordExists ? 'SUCCESS' : 'FAILED',
          )

          if (!recordExists) {
            throw new Error(`Record ${record.id} was not persisted properly`)
          }
        }
      }

      // Commit transaction
      await executeQuery('COMMIT')
      console.log('Transaction committed successfully')
    } catch (insertError) {
      // Rollback transaction on error
      await executeQuery('ROLLBACK')
      console.error('Transaction rolled back due to error:', insertError)
      throw insertError
    }

    // Verify data was actually saved by checking database
    console.log('Verifying data was saved to database...')
    const verifyResult = await executeQuery(
      `SELECT COUNT(*) as count FROM ${tableName} WHERE customer_id = ? AND division_id = ?`,
      [data.customer_id, data.division_id],
    )

    console.log('Verify result raw:', verifyResult)
    const recordCount = verifyResult?.result?.resultRows?.[0]?.[0] || 0
    console.log('Records found in database after insert:', recordCount)

    // Also check all records in table
    const allRecordsResult = await executeQuery(`SELECT * FROM ${tableName}`)
    console.log('All records in table:', allRecordsResult?.result?.resultRows?.length || 0)
    console.log('All records data:', allRecordsResult?.result?.resultRows)

    // Force database sync to ensure persistence
    try {
      console.log('Starting database sync operations...')

      // Set synchronous mode to FULL for maximum durability
      const syncResult = await executeQuery('PRAGMA synchronous = FULL')
      console.log('Synchronous mode set:', syncResult)

      // Enable WAL mode for better concurrency
      const walResult = await executeQuery('PRAGMA journal_mode = WAL')
      console.log('WAL mode enabled:', walResult)

      // Force checkpoint to write WAL to main database
      const checkpointResult = await executeQuery('PRAGMA wal_checkpoint(FULL)')
      console.log('WAL checkpoint completed:', checkpointResult)

      // Force vacuum to ensure data is written
      const vacuumResult = await executeQuery('VACUUM')
      console.log('Database vacuum completed:', vacuumResult)

      // Additional sync
      const additionalSyncResult = await executeQuery('PRAGMA synchronous = FULL')
      console.log('Additional sync completed:', additionalSyncResult)

      console.log('Database sync completed for persistence')
    } catch (syncError) {
      console.error('Database sync error:', syncError)
      // Try alternative sync method
      try {
        await executeQuery('PRAGMA synchronous = NORMAL')
        await executeQuery('PRAGMA journal_mode = DELETE')
        console.log('Fallback sync method applied')
      } catch (fallbackError) {
        console.error('Fallback sync failed:', fallbackError)
      }
    }

    if (recordCount === 0) {
      throw new Error('Data was not saved to database - verification failed')
    }

    // Final verification after sync operations
    console.log('Performing final verification after sync...')
    const finalVerificationResult = await executeQuery(`SELECT COUNT(*) as count FROM ${tableName}`)
    const finalRecordCount = finalVerificationResult?.result?.resultRows?.[0]?.[0] || 0
    console.log('Final record count after sync:', finalRecordCount)

    if (finalRecordCount === 0) {
      throw new Error('Data was lost after sync operations - persistence failed')
    }

    // Return the created item (convert back to hierarchical for UI)
    const createdItem = await getWorkoutCustomerChargeByCustomerDivision(
      data.customer_id,
      data.division_id,
    )
    if (!createdItem) {
      throw new Error('Failed to retrieve created workout customer charge')
    }

    console.log('Workout customer charge created successfully:', createdItem)
    return createdItem
  } catch (error) {
    console.error('Error creating workout customer charge:', error)
    throw error
  }
}

// Update Workout Customer Charge
export const updateWorkoutCustomerCharge = async (
  id: string,
  data: WorkoutCustomerChargeFormData,
): Promise<WorkoutCustomerCharge> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getWorkoutCustomerChargeTableName()
    const now = new Date().toISOString()

    console.log('Updating workout customer charge with ID:', id, 'data:', data)

    // Don't delete existing records - use UPSERT logic instead
    console.log('Using UPSERT logic for update - no data will be deleted')

    // Convert hierarchical data to flat records and insert new ones
    const flatRecords: WorkoutCustomerChargeFlat[] = []

    Object.entries(data.workout_customer_charge_content).forEach(
      ([_, categoryData]: [string, any]) => {
        Object.entries(categoryData.workouts || {}).forEach(([_, workoutData]: [string, any]) => {
          Object.entries(workoutData.years || {}).forEach(([year, yearData]: [string, any]) => {
            const record: WorkoutCustomerChargeFlat = {
              id: generateWorkoutChargeId(),
              customer_id: data.customer_id,
              division_id: data.division_id,
              category_id: categoryData.category_id,
              workout_id: workoutData.group_id,
              year: parseInt(year),
              month_1: null,
              month_2: null,
              month_3: null,
              month_4: null,
              month_5: null,
              month_6: null,
              month_7: null,
              month_8: null,
              month_9: null,
              month_10: null,
              month_11: null,
              month_12: null,
              created_at: now,
              updated_at: now,
            }

            // Set month quantities
            Object.entries(yearData).forEach(([_, monthData]: [string, any]) => {
              const monthNum = monthData.month
              if (monthNum >= 1 && monthNum <= 12) {
                ;(record as any)[`month_${monthNum}`] = monthData.quantity || 0
              }
            })

            flatRecords.push(record)
          })
        })
      },
    )

    // UPSERT all flat records (same logic as create)
    for (const record of flatRecords) {
      console.log('Processing record for update (UPSERT):', record)

      // Check if record exists
      const existingRecordResult = await executeQuery(
        `SELECT id FROM ${tableName} WHERE customer_id = ? AND division_id = ? AND category_id = ? AND workout_id = ? AND year = ?`,
        [
          record.customer_id,
          record.division_id,
          record.category_id,
          record.workout_id,
          record.year,
        ],
      )

      let upsertResult
      if (
        existingRecordResult?.result?.resultRows &&
        existingRecordResult.result.resultRows.length > 0
      ) {
        // Record exists - UPDATE
        console.log('Record exists, updating...')
        upsertResult = await executeQuery(
          `
          UPDATE ${tableName} SET
            month_1 = ?, month_2 = ?, month_3 = ?, month_4 = ?, month_5 = ?, month_6 = ?,
            month_7 = ?, month_8 = ?, month_9 = ?, month_10 = ?, month_11 = ?, month_12 = ?,
            updated_at = ?
          WHERE customer_id = ? AND division_id = ? AND category_id = ? AND workout_id = ? AND year = ?
        `,
          [
            record.month_1,
            record.month_2,
            record.month_3,
            record.month_4,
            record.month_5,
            record.month_6,
            record.month_7,
            record.month_8,
            record.month_9,
            record.month_10,
            record.month_11,
            record.month_12,
            record.updated_at,
            record.customer_id,
            record.division_id,
            record.category_id,
            record.workout_id,
            record.year,
          ],
        )
      } else {
        // Record doesn't exist - INSERT
        console.log('Record does not exist, inserting...')
        upsertResult = await executeQuery(
          `
          INSERT INTO ${tableName} (
            id, customer_id, division_id, category_id, workout_id, year,
            month_1, month_2, month_3, month_4, month_5, month_6,
            month_7, month_8, month_9, month_10, month_11, month_12,
            created_at, updated_at
          )
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `,
          [
            record.id,
            record.customer_id,
            record.division_id,
            record.category_id,
            record.workout_id,
            record.year,
            record.month_1,
            record.month_2,
            record.month_3,
            record.month_4,
            record.month_5,
            record.month_6,
            record.month_7,
            record.month_8,
            record.month_9,
            record.month_10,
            record.month_11,
            record.month_12,
            record.created_at,
            record.updated_at,
          ],
        )
      }

      if (!upsertResult || upsertResult.type !== 'exec') {
        throw new Error('Failed to upsert updated workout customer charge record')
      } else {
        console.log('Successfully upserted record during update with ID:', record.id)
      }
    }

    // Return the updated item (convert back to hierarchical for UI)
    const updatedItem = await getWorkoutCustomerChargeByCustomerDivision(
      data.customer_id,
      data.division_id,
    )
    if (!updatedItem) {
      throw new Error('Failed to retrieve updated workout customer charge')
    }

    console.log('Workout customer charge updated successfully:', updatedItem)
    return updatedItem
  } catch (error) {
    console.error('Error updating workout customer charge:', error)
    throw error
  }
}

// Delete Workout Customer Charge
export const deleteWorkoutCustomerCharge = async (id: string): Promise<void> => {
  const { executeQuery } = useSQLite()

  try {
    const tableName = getWorkoutCustomerChargeTableName()

    console.log('Deleting workout customer charge with ID:', id)

    const result = await executeQuery(`DELETE FROM ${tableName} WHERE id = ?`, [id])

    if (!result || result.type !== 'exec') {
      throw new Error('Failed to delete workout customer charge')
    }

    console.log('Workout customer charge deleted successfully')
  } catch (error) {
    console.error('Error deleting workout customer charge:', error)
    throw error
  }
}

// Get customers for dropdown
export const getCustomersForDropdown = async (): Promise<CustomerOption[]> => {
  const { executeQuery } = useSQLite()

  try {
    const result = await executeQuery(
      `SELECT id, name, code FROM customers WHERE status = 'active' ORDER BY name`,
    )

    if (!result?.result?.resultRows) {
      return []
    }

    return result.result.resultRows.map((row) => ({
      id: row[0] as number,
      name: row[1] as string,
      code: row[2] as string,
    }))
  } catch (error) {
    console.error('Error getting customers for dropdown:', error)
    return []
  }
}

// Get divisions for dropdown by customer ID
export const getDivisionsByCustomerId = async (customerId: number): Promise<DivisionOption[]> => {
  const { executeQuery } = useSQLite()

  try {
    const result = await executeQuery(
      `SELECT id, name, code, customer_id FROM customer_divisions WHERE customer_id = ? AND status = 'active' ORDER BY name`,
      [customerId],
    )

    if (!result?.result?.resultRows) {
      return []
    }

    return result.result.resultRows.map((row) => ({
      id: row[0] as number,
      name: row[1] as string,
      code: row[2] as string,
      customer_id: row[3] as number,
    }))
  } catch (error) {
    console.error('Error getting divisions by customer ID:', error)
    return []
  }
}

// Get categories for dropdown
export const getCategoriesForDropdown = async (): Promise<CategoryOption[]> => {
  const { executeQuery } = useSQLite()

  try {
    console.log('Getting categories for dropdown...')

    // Try different possible table names for categories
    const possibleTableNames = ['categories', 'category', 'product_categories']
    let result = null
    let usedTableName = ''

    for (const tableName of possibleTableNames) {
      try {
        console.log(`Trying categories table: ${tableName}`)
        result = await executeQuery(`SELECT id, name FROM ${tableName} ORDER BY name`)
        if (result?.result?.resultRows && result.result.resultRows.length > 0) {
          usedTableName = tableName
          console.log(`Success with categories table: ${tableName}`, result?.result?.resultRows)
          break
        }
      } catch (tableError) {
        console.log(`Categories table ${tableName} not found or error:`, tableError)
        continue
      }
    }

    if (!result?.result?.resultRows) {
      console.log('No categories found, returning mock data')
      // Return mock data for testing
      return [
        { id: 'CAT001', name: 'Fitness Training' },
        { id: 'CAT002', name: 'Strength Training' },
        { id: 'CAT003', name: 'Cardio Workout' },
        { id: 'CAT004', name: 'Yoga & Meditation' },
        { id: 'CAT005', name: 'Sports Training' },
      ]
    }

    const categories = result.result.resultRows.map((row) => ({
      id: row[0] as string,
      name: row[1] as string,
    }))

    console.log(`Found ${categories.length} categories in table ${usedTableName}:`, categories)
    return categories
  } catch (error) {
    console.error('Error getting categories for dropdown:', error)
    // Return mock data as fallback
    return [
      { id: 'CAT001', name: 'Fitness Training' },
      { id: 'CAT002', name: 'Strength Training' },
      { id: 'CAT003', name: 'Cardio Workout' },
      { id: 'CAT004', name: 'Yoga & Meditation' },
      { id: 'CAT005', name: 'Sports Training' },
    ]
  }
}

// Get category workouts for dropdown by category ID
export const getCategoryWorkoutsByCategoryId = async (
  categoryId: string,
): Promise<CategoryWorkoutOption[]> => {
  const { executeQuery } = useSQLite()

  try {
    console.log('Getting category workouts for category ID:', categoryId)

    // First, let's check what tables exist and their structure
    const tablesResult = await executeQuery(`SELECT name FROM sqlite_master WHERE type='table'`)
    console.log('Available tables:', tablesResult?.result?.resultRows)

    // Try different possible table names for category workouts
    const possibleTableNames = [
      'workout',
      'customer_workout',
      'category_workout',
      'workout_categories',
      'workouts',
    ]
    let result = null
    let usedTableName = ''

    for (const tableName of possibleTableNames) {
      try {
        console.log(`Trying table: ${tableName}`)
        // Try different column combinations
        const queries = [
          `SELECT id, name, unit_price, category_id, calculation_type FROM ${tableName} WHERE category_id = ? ORDER BY name`,
          `SELECT id, name, price as unit_price, category_id, calculation_type FROM ${tableName} WHERE category_id = ? ORDER BY name`,
          `SELECT id, name, unit_price, cate_id as category_id, calculation_type FROM ${tableName} WHERE cate_id = ? ORDER BY name`,
          `SELECT id, name, price as unit_price, cate_id as category_id, calculation_type FROM ${tableName} WHERE cate_id = ? ORDER BY name`,
          // fallback queries with calculationType (old field name)
          `SELECT id, name, unit_price, category_id, calculationType as calculation_type FROM ${tableName} WHERE category_id = ? ORDER BY name`,
          `SELECT id, name, price as unit_price, category_id, calculationType as calculation_type FROM ${tableName} WHERE category_id = ? ORDER BY name`,
          `SELECT id, name, unit_price, cate_id as category_id, calculationType as calculation_type FROM ${tableName} WHERE cate_id = ? ORDER BY name`,
          `SELECT id, name, price as unit_price, cate_id as category_id, calculationType as calculation_type FROM ${tableName} WHERE cate_id = ? ORDER BY name`,
          // fallback queries without calculation_type
          `SELECT id, name, unit_price, category_id FROM ${tableName} WHERE category_id = ? ORDER BY name`,
          `SELECT id, name, price as unit_price, category_id FROM ${tableName} WHERE category_id = ? ORDER BY name`,
          `SELECT id, name, unit_price, cate_id as category_id FROM ${tableName} WHERE cate_id = ? ORDER BY name`,
          `SELECT id, name, price as unit_price, cate_id as category_id FROM ${tableName} WHERE cate_id = ? ORDER BY name`,
        ]

        for (const query of queries) {
          try {
            result = await executeQuery(query, [categoryId])
            if (result?.result?.resultRows && result.result.resultRows.length > 0) {
              usedTableName = tableName
              console.log(
                `Success with table: ${tableName} and query: ${query}`,
                result?.result?.resultRows,
              )
              break
            }
          } catch (queryError) {
            console.log(`Query failed: ${query}`, queryError)
            continue
          }
        }

        if (result?.result?.resultRows && result.result.resultRows.length > 0) {
          break
        }
      } catch (tableError) {
        console.log(`Table ${tableName} not found or error:`, tableError)
        continue
      }
    }

    if (!result?.result?.resultRows) {
      console.log('No category workouts found for category:', categoryId)
      // Return some mock data for testing
      return [
        {
          id: `WORKOUT_${categoryId}_1`,
          name: `Workout 1 for Category ${categoryId}`,
          unit_price: 50000,
          category_id: categoryId,
          calculation_type: 'Auto',
        },
        {
          id: `WORKOUT_${categoryId}_2`,
          name: `Workout 2 for Category ${categoryId}`,
          unit_price: 75000,
          category_id: categoryId,
          calculation_type: 'Manual',
        },
      ]
    }

    const workouts = result.result.resultRows.map((row) => ({
      id: row[0] as string,
      name: row[1] as string,
      unit_price: row[2] as number,
      category_id: row[3] as string,
      calculation_type: row[4] as string | undefined, // May be undefined if not present
    }))

    console.log(`Found ${workouts.length} workouts in table ${usedTableName}:`, workouts)
    return workouts
  } catch (error) {
    console.error('Error getting category workouts by category ID:', error)
    // Return mock data as fallback
    return [
      {
        id: `WORKOUT_${categoryId}_MOCK_1`,
        name: `Mock Workout 1 for Category ${categoryId}`,
        unit_price: 100000,
        category_id: categoryId,
        calculation_type: 'Auto',
      },
      {
        id: `WORKOUT_${categoryId}_MOCK_2`,
        name: `Mock Workout 2 for Category ${categoryId}`,
        unit_price: 150000,
        category_id: categoryId,
        calculation_type: 'Manual',
      },
    ]
  }
}

// Delete ALL workout customer charge records for a customer/division
export const deleteWorkoutCustomerChargeById = async (id: string): Promise<void> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Deleting workout customer charge by ID:', id)

    // First get the customer_id and division_id from the record
    const getRecordSQL = `
      SELECT customer_id, division_id FROM ${tables.workout_customer_charge}
      WHERE id = ? LIMIT 1
    `
    const recordResult = await executeQuery(getRecordSQL, [id])

    if (!recordResult?.result?.resultRows || recordResult.result.resultRows.length === 0) {
      console.log('No record found with ID:', id)
      throw new Error(`No workout charge found with ID: ${id}`)
    }

    const customerId = recordResult.result.resultRows[0][0]
    const divisionId = recordResult.result.resultRows[0][1]

    console.log('Found record - Customer ID:', customerId, 'Division ID:', divisionId)

    // Delete ALL records for this customer/division (not just one record)
    const deleteSQL = `
      DELETE FROM ${tables.workout_customer_charge}
      WHERE customer_id = ? AND division_id = ?
    `
    const params = [customerId, divisionId]

    console.log('Delete query:', deleteSQL, 'Params:', params)
    const result = await executeQuery(deleteSQL, params)
    console.log('Delete result:', result)

    if (result?.type === 'exec') {
      console.log('Successfully deleted ALL workout customer charge records for customer/division')

      // Verify deletion
      const verifySQL = `
        SELECT COUNT(*) as count FROM ${tables.workout_customer_charge}
        WHERE customer_id = ? AND division_id = ?
      `
      const verifyResult = await executeQuery(verifySQL, [customerId, divisionId])
      const remainingCount = Number(verifyResult?.result?.resultRows?.[0]?.[0]) || 0
      console.log('Records remaining after delete:', remainingCount)

      if (remainingCount > 0) {
        throw new Error('Records were not deleted properly')
      }
    } else {
      throw new Error('Failed to delete workout customer charge records')
    }
  } catch (error) {
    console.error('Error deleting workout customer charge by ID:', error)
    throw error
  }
}
