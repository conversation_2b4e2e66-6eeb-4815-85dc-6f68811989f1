import { useSQLite } from '../../../hooks/useSQLite'
import type {
  AutoFunction,
  AutoFunctionFormData,
  AutoFunctionFilters,
} from '../../../types/AutoFunction'

// Initialize Auto Function database table
export const initializeAutoFocusDB = async (): Promise<void> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Initializing Auto Function database...')

    // Force recreate tables to ensure correct schema
    console.log('Dropping existing auto function tables...')
    try {
      await executeQuery(`DROP TABLE IF EXISTS ${tables.auto_function_customers}`)
      await executeQuery(`DROP TABLE IF EXISTS ${tables.auto_function}`)
      console.log('Old tables dropped successfully')
    } catch (dropError) {
      console.log('Error dropping tables (may not exist):', dropError)
    }

    // Create auto_function table with correct schema
    console.log('Creating auto_function table...')
    await executeQuery(`
      CREATE TABLE ${tables.auto_function} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        code TEXT NOT NULL UNIQUE,
        name TEXT NOT NULL,
        note TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `)

    // Create auto_function_customers junction table
    console.log('Creating auto_function_customers table...')
    await executeQuery(`
      CREATE TABLE ${tables.auto_function_customers} (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        auto_function_id INTEGER NOT NULL,
        customer_id INTEGER NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (auto_function_id) REFERENCES ${tables.auto_function} (id) ON DELETE CASCADE,
        FOREIGN KEY (customer_id) REFERENCES ${tables.customers} (id) ON DELETE CASCADE,
        UNIQUE(auto_function_id, customer_id)
      )
    `)

    console.log('Tables created successfully, seeding data...')
    await seedAutoFocusData()

    console.log('Auto Function database initialized successfully')
  } catch (error) {
    console.error('Error initializing Auto Function database:', error)
    throw error
  }
}

// Seed initial Auto Function data
const seedAutoFocusData = async (): Promise<void> => {
  const { executeQuery, tables } = useSQLite()

  try {
    // Check if data already exists
    const checkExisting = await executeQuery(
      `SELECT COUNT(*) as count FROM ${tables.auto_function}`,
    )
    const count = (checkExisting?.result?.resultRows?.[0]?.[0] as number) || 0

    if (count > 0) {
      console.log('Auto function data already exists, skipping seed data')
      return
    }

    // Find Nitori customer ID
    console.log('Finding Nitori customer...')
    const nitoriResult = await executeQuery(
      `SELECT id FROM ${tables.customers} WHERE code = ? OR name = ?`,
      ['CUS002', 'Nitori'],
    )

    const nitoriId = nitoriResult?.result?.resultRows?.[0]?.[0] as number | undefined

    if (!nitoriId) {
      console.warn('Nitori customer not found, creating auto function without customer link')
    } else {
      console.log('Found Nitori customer with ID:', nitoriId)
    }

    // Insert initial auto functions
    const now = new Date().toISOString()

    const autoFunctions = [
      {
        code: 'N_A_IN_SUM_CARTON',
        name: 'Nitory Inbound Auto Calculation Total Cartons Received',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_IN_SUM_PCS',
        name: 'Nitory Inbound Auto Calculation Total Pieces Received',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_IN_SUM_AI',
        name: 'Nitory Inbound Auto  Calculation Total AI Pieces',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_IN_SUM_M3',
        name: 'Nitory Inbound Auto Calculation  Total m3',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_ALL_SUM_CARTON',
        name: 'Nitory Outbound Auto  Calculation Total Cartons Received',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_ALL_SUM_PCS',
        name: 'Nitory Outbound Auto  Calculation Total Pieces Received',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_ALL_SUM_M3',
        name: 'Nitory Outbound Auto Calculation  Total m3',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_STORE_SUM_CARTON',
        name: 'Nitory Outbound Auto Calculation Total Cartons Received for Store',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_STORE_SUM_PCS',
        name: 'Nitory Outbound Auto Calculation Total Pieces Received for Store',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_STORE_SUM_M3',
        name: 'Nitory Outbound Auto Calculation  Total m3 for Store',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_CUSTOMER_SUM_CARTON',
        name: 'Nitory Outbound Auto Calculation Total Cartons Received for Customer',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_CUSTOMER_SUM_PCS',
        name: 'Nitory Outbound Auto Calculation Total Pieces Received for Customer',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_CUSTOMER_SUM_M3',
        name: 'Nitory Outbound Auto Calculation  Total m3 for Customer',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_CUSTOMER_SUM_LABEL',
        name: 'Nitory Outbound Auto Calculation Total Labeling',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_NONE_SUM_CARTON',
        name: 'Nitory Outbound Auto  Calculation Total Cartons without COD',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_NONE_SUM_PCS',
        name: 'Nitory Outbound Auto  Calculation Total Pieces without COD',
        note: 'For Nitory Only',
      },
      {
        code: 'N_A_OUT_NONE_SUM_M3',
        name: 'Nitory Outbound Auto Calculation Total m3 without COD',
        note: 'For Nitory Only',
      },
    ]

    console.log('Creating initial auto functions...')

    for (const autoFunc of autoFunctions) {
      // Insert auto function
      await executeQuery(
        `
        INSERT INTO ${tables.auto_function} (code, name, note, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?)
      `,
        [autoFunc.code, autoFunc.name, autoFunc.note, now, now],
      )

      // Get the inserted auto function ID
      const autoFunctionResult = await executeQuery(
        `SELECT MAX(id) as maxId FROM ${tables.auto_function}`,
      )
      const autoFunctionId = autoFunctionResult?.result?.resultRows?.[0]?.[0] as number | undefined

      // Link with Nitori customer if found
      if (nitoriId && autoFunctionId) {
        console.log(`Linking auto function ${autoFunc.code} with Nitori customer...`)
        await executeQuery(
          `
          INSERT INTO ${tables.auto_function_customers} (auto_function_id, customer_id, created_at)
          VALUES (?, ?, ?)
        `,
          [autoFunctionId, nitoriId, now],
        )
        console.log(`Auto function ${autoFunc.code} linked with Nitori customer successfully`)
      }
    }

    console.log('Initial auto function created successfully')
  } catch (error) {
    console.error('Error seeding auto function data:', error)
    throw error
  }
}

// Get all Auto Function items with optional filters
export const getAutoFocusItems = async (filters?: AutoFunctionFilters): Promise<AutoFunction[]> => {
  const { executeQuery, tables } = useSQLite()

  try {
    // First get all auto function items
    let query = `
      SELECT
        af.id,
        af.code,
        af.name,
        af.note,
        af.created_at as createdAt,
        af.updated_at as updatedAt
      FROM ${tables.auto_function} af
    `

    const params: any[] = []
    const conditions: string[] = []

    if (filters?.search) {
      conditions.push('(af.code LIKE ? OR af.name LIKE ? OR af.note LIKE ?)')
      const searchTerm = `%${filters.search}%`
      params.push(searchTerm, searchTerm, searchTerm)
    }

    if (filters?.customerId) {
      conditions.push(`af.id IN (
        SELECT afc.auto_function_id
        FROM ${tables.auto_function_customers} afc
        WHERE afc.customer_id = ?
      )`)
      params.push(filters.customerId)
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ')
    }

    query += ' ORDER BY af.id ASC'

    const result = await executeQuery(query, params)

    if (!result?.result?.resultRows) {
      return []
    }

    const autoFocusItems: AutoFunction[] = []

    // For each auto focus item, get its customers
    for (const row of result.result.resultRows) {
      const autoFocusId = row[0]

      // Get customers for this auto function
      const customerQuery = `
        SELECT afc.customer_id, c.name
        FROM ${tables.auto_function_customers} afc
        LEFT JOIN ${tables.customers} c ON afc.customer_id = c.id
        WHERE afc.auto_function_id = ?
        ORDER BY c.name
      `

      const customerResult = await executeQuery(customerQuery, [autoFocusId])

      const customerIds: number[] = []
      const customerNames: string[] = []

      if (customerResult?.result?.resultRows) {
        for (const customerRow of customerResult.result.resultRows) {
          customerIds.push(customerRow[0] as number)
          customerNames.push((customerRow[1] as string) || 'Unknown')
        }
      }

      autoFocusItems.push({
        id: row[0] as number,
        code: row[1] as string,
        name: row[2] as string,
        customerIds,
        customerNames,
        note: row[3] as string,
        createdAt: row[4] as string,
        updatedAt: row[5] as string,
      })
    }

    return autoFocusItems
  } catch (error) {
    console.error('Error getting Auto Function items:', error)
    throw error
  }
}

// Get Auto Function item by ID
export const getAutoFocusById = async (id: number): Promise<AutoFunction | null> => {
  const { executeQuery, tables } = useSQLite()

  try {
    const result = await executeQuery(
      `
      SELECT
        af.id,
        af.code,
        af.name,
        af.note,
        af.created_at as createdAt,
        af.updated_at as updatedAt
      FROM ${tables.auto_function} af
      WHERE af.id = ?
    `,
      [id],
    )

    if (!result?.result?.resultRows?.[0]) {
      return null
    }

    const row = result.result.resultRows[0]

    // Get customers for this auto function
    const customerQuery = `
      SELECT afc.customer_id, c.name
      FROM ${tables.auto_function_customers} afc
      LEFT JOIN ${tables.customers} c ON afc.customer_id = c.id
      WHERE afc.auto_function_id = ?
      ORDER BY c.name
    `

    const customerResult = await executeQuery(customerQuery, [id])

    const customerIds: number[] = []
    const customerNames: string[] = []

    if (customerResult?.result?.resultRows) {
      for (const customerRow of customerResult.result.resultRows) {
        customerIds.push(customerRow[0] as number)
        customerNames.push((customerRow[1] as string) || 'Unknown')
      }
    }

    return {
      id: row[0] as number,
      code: row[1] as string,
      name: row[2] as string,
      customerIds,
      customerNames,
      note: row[3] as string,
      createdAt: row[4] as string,
      updatedAt: row[5] as string,
    }
  } catch (error) {
    console.error('Error getting Auto Function by ID:', error)
    throw error
  }
}

// Create new Auto Function item
export const createAutoFocus = async (data: AutoFunctionFormData): Promise<AutoFunction> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Creating Auto Function with data:', data)

    // Validate input data
    if (!data.code || !data.name || !data.customerIds || data.customerIds.length === 0) {
      throw new Error('Invalid input data: code, name, and customerIds are required')
    }

    const now = new Date().toISOString()

    // Insert auto function record
    console.log('Inserting auto function record...')
    const result = await executeQuery(
      `
      INSERT INTO ${tables.auto_function} (code, name, note, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?)
    `,
      [data.code, data.name, data.note || '', now, now],
    )
    console.log('Auto function insert result:', result)

    // Get the inserted ID
    console.log('Getting inserted ID...')
    const maxIdResult = await executeQuery(`SELECT MAX(id) as maxId FROM ${tables.auto_function}`)
    console.log('Max ID result:', maxIdResult)

    const newId = (maxIdResult?.result?.resultRows?.[0]?.[0] as number) || 0
    console.log('New auto focus ID:', newId)

    if (!newId || newId === 0) {
      throw new Error('Failed to get valid auto focus ID after insert')
    }

    // Insert customer relationships
    console.log('Inserting customer relationships for customers:', data.customerIds)
    for (const customerId of data.customerIds) {
      console.log('Inserting relationship for customer:', customerId)
      await executeQuery(
        `
        INSERT INTO ${tables.auto_function_customers} (auto_function_id, customer_id, created_at)
        VALUES (?, ?, ?)
      `,
        [newId, customerId, now],
      )
    }
    console.log('Customer relationships inserted successfully')

    // Return the created item
    console.log('Retrieving created item...')
    const createdItem = await getAutoFocusById(newId)
    if (!createdItem) {
      throw new Error('Failed to retrieve created Auto Function item')
    }

    console.log('Auto Function created successfully:', createdItem)
    return createdItem
  } catch (error) {
    console.error('Error creating Auto Function:', error)
    throw error
  }
}

// Update Auto Function item
export const updateAutoFocus = async (
  id: number,
  data: AutoFunctionFormData,
): Promise<AutoFunction> => {
  const { executeQuery, tables } = useSQLite()

  try {
    const now = new Date().toISOString()

    // Update auto function record
    await executeQuery(
      `
      UPDATE ${tables.auto_function}
      SET code = ?, name = ?, note = ?, updated_at = ?
      WHERE id = ?
    `,
      [data.code, data.name, data.note, now, id],
    )

    // Delete existing customer relationships
    await executeQuery(
      `
      DELETE FROM ${tables.auto_function_customers}
      WHERE auto_function_id = ?
    `,
      [id],
    )

    // Insert new customer relationships
    for (const customerId of data.customerIds) {
      await executeQuery(
        `
        INSERT INTO ${tables.auto_function_customers} (auto_function_id, customer_id, created_at)
        VALUES (?, ?, ?)
      `,
        [id, customerId, now],
      )
    }

    // Return the updated item
    const updatedItem = await getAutoFocusById(id)
    if (!updatedItem) {
      throw new Error('Failed to retrieve updated Auto Function item')
    }

    return updatedItem
  } catch (error) {
    console.error('Error updating Auto Function:', error)
    throw error
  }
}

// Delete Auto Function item
export const deleteAutoFocus = async (id: number): Promise<void> => {
  const { executeQuery, tables } = useSQLite()

  try {
    console.log('Deleting Auto Function with ID:', id)

    // First delete customer relationships
    console.log('Deleting customer relationships...')
    await executeQuery(`DELETE FROM ${tables.auto_function_customers} WHERE auto_function_id = ?`, [
      id,
    ])

    // Then delete the auto function record
    console.log('Deleting auto function record...')
    const result = await executeQuery(`DELETE FROM ${tables.auto_function} WHERE id = ?`, [id])
    console.log('Delete result:', result)

    console.log('Auto Function deleted successfully')
  } catch (error) {
    console.error('Error deleting Auto Function:', error)
    throw error
  }
}
