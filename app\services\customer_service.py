from ..dao.customer_dao import CustomerDAO

class CustomerService:
    def __init__(self):
        self.customer_dao = CustomerDAO()

    def get_all_customers(self):
        return self.customer_dao.get_all_customers()

    def get_customer_by_id(self, customer_id):
        return self.customer_dao.get_customer_by_id(customer_id)

    def add_customer(self, customer_data):
        # Validate required fields
        required_fields = ['name']
        for field in required_fields:
            if field not in customer_data or not customer_data[field]:
                raise ValueError(f"Missing required field: {field}")

        return self.customer_dao.add_customer(customer_data)

    def update_customer(self, customer_data):
        if 'id' not in customer_data:
            raise ValueError("Customer ID is required for update")

        # Check if customer exists
        existing_customer = self.customer_dao.get_customer_by_id(customer_data['id'])
        if not existing_customer:
            raise ValueError("Customer not found")

        return self.customer_dao.update_customer(customer_data)

    def delete_customer(self, customer_id):
        # Check if customer exists
        customer = self.customer_dao.get_customer_by_id(customer_id)
        if not customer:
            raise ValueError("Customer not found")

        # Soft delete customer (no status validation needed for soft delete)
        return self.customer_dao.delete_customer(customer_id)

    # Customer Settings methods
    def get_customer_settings(self, customer_id):
        return self.customer_dao.get_customer_settings(customer_id)

    def add_or_update_customer_settings(self, settings_data):
        if 'customerId' not in settings_data:
            raise ValueError("Customer ID is required")

        # Check if customer exists
        customer = self.customer_dao.get_customer_by_id(settings_data['customerId'])
        if not customer:
            raise ValueError("Customer not found")

        return self.customer_dao.add_or_update_customer_settings(settings_data)

    def delete_customer_settings(self, customer_id):
        # Check if customer exists
        customer = self.customer_dao.get_customer_by_id(customer_id)
        if not customer:
            raise ValueError("Customer not found")

        # Check if settings exist
        settings = self.customer_dao.get_customer_settings(customer_id)
        if not settings:
            raise ValueError("Customer settings not found")

        return self.customer_dao.delete_customer_settings(customer_id)

    # Customer Divisions methods
    def get_customer_divisions_by_customer_id(self, customer_id):
        return self.customer_dao.get_customer_divisions_by_customer_id(customer_id)

    def get_customer_division_by_id(self, division_id):
        return self.customer_dao.get_customer_division_by_id(division_id)

    def add_customer_division(self, division_data):
        # Validate required fields
        required_fields = ['name', 'customerId']
        for field in required_fields:
            if field not in division_data or not division_data[field]:
                raise ValueError(f"Missing required field: {field}")

        # Check if customer exists
        customer = self.customer_dao.get_customer_by_id(division_data['customerId'])
        if not customer:
            raise ValueError("Customer not found")

        return self.customer_dao.add_customer_division(division_data)

    def update_customer_division(self, division_data):
        if 'id' not in division_data:
            raise ValueError("Division ID is required for update")

        # Check if division exists
        existing_division = self.customer_dao.get_customer_division_by_id(division_data['id'])
        if not existing_division:
            raise ValueError("Customer division not found")

        return self.customer_dao.update_customer_division(division_data)

    def delete_customer_division(self, division_id):
        # Check if division exists
        division = self.customer_dao.get_customer_division_by_id(division_id)
        if not division:
            raise ValueError("Customer division not found")

        # Soft delete division (no status validation needed for soft delete)
        return self.customer_dao.delete_customer_division(division_id)
